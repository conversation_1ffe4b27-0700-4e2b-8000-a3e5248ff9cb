package amobi.module.compose.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight


object AppFontFamily {
    fun get() = FontFamily(
        Font(R.font.manrope_regular, FontWeight.Normal),
        Font(R.font.manrope_bold, FontWeight.Bold),
        Font(R.font.manrope_medium, FontWeight.Medium),
        Font(R.font.manrope_extrabold, FontWeight.ExtraBold),
        Font(R.font.manrope_extralight, FontWeight.ExtraLight),
        Font(R.font.manrope_light, FontWeight.Light),
        Font(R.font.manrope_semibold, FontWeight.SemiBold),
    )
}
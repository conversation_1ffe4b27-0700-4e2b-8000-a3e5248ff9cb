package com.amobilab.ezmath.ai.presentation.ui.home.values

import androidx.annotation.StringRes
import com.amobilab.ezmath.ai.R

enum class SettingSystemOptions(
    val icon: Int,
    @StringRes val stringsId: Int
) {
    Theme(
        icon = R.drawable.ic_setting_themes_new,
        stringsId = R.string.txtid_theme
    ),
    AIAssistance(
        icon = R.drawable.ic_setting_ai_assistance,
        stringsId = R.string.ai_assistance
    ),
    HistoryCoin(
        icon = R.drawable.ic_setting_credit_history,
        stringsId = R.string.history_coin
    ),
}
package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.compose.theme.AppColors
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp


@Composable
fun CustomCheckbox(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    checkedColor: Color = AppColors.current.checkColor,
    uncheckedColor: Color = AppColors.current.unCheckColor,
    icon: ImageVector = Icons.Default.Check,
    cornerRadius: Dp = 6.dp
) {
    Surface(
        modifier = modifier
            .size(24.dp)
            .clickable { onCheckedChange(!checked) },
        color = if (checked) checkedColor else uncheckedColor,
        shape = RoundedCornerShape(cornerRadius),
    ) {
        Box(contentAlignment = Alignment.Center) {
            if (checked) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

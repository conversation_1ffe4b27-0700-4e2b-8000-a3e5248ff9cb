package com.amobilab.ezmath.ai.presentation.common.shared_components

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.debugLog
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.cardview.widget.CardView
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Message
import com.amobilab.ezmath.ai.utils.renderMarkdown
import com.github.chrisbanes.photoview.PhotoView

class MessageAdapter(
    var messageList: MutableList<Message>,
    var backgroundLeftChat: Color,
    var backgroundRightChat: Color,
    var textLeftColor: Color,
    var textRightColor: Color,
    private val onResendMessage: (Message) -> Unit
) : RecyclerView.Adapter<MessageAdapter.MessageViewHolder>() {
    @SuppressLint("SetJavaScriptEnabled")
    inner class MessageViewHolder(var v: View) : RecyclerView.ViewHolder(v) {
        private val leftChatView: LinearLayout = v.findViewById(R.id.left_chat_view)
        private val leftTextView: WebView = v.findViewById(R.id.left_chat_text_view)
        private val leftTextError: TextView = v.findViewById(R.id.left_chat_text_error)
        private val rightChatView: LinearLayout = v.findViewById(R.id.right_chat_view)
        private val rightTextView: TextView = v.findViewById(R.id.right_chat_text_view)
        private val rightCardView: CardView = v.findViewById(R.id.roundedCornerCardView)
        private val rightImageView: ImageView = v.findViewById(R.id.roundedCornerImageView)
        private val animationView: LottieAnimationView = v.findViewById(R.id.animationView)

        //        private val aiName: TextView = v.findViewById(R.id.ai_name)
        private val resendIcon: ImageView = v.findViewById(R.id.resend_icon)
        private val copyText: LinearLayout = v.findViewById(R.id.copy_text_view)
        private val leftChatModel: ImageView = v.findViewById(R.id.left_chat_model)

        private var isOnPageFinished = false

        init {
            v.findViewById<LinearLayout>(R.id.left_chat_container)?.backgroundTintList =
                ColorStateList.valueOf(
                    backgroundLeftChat.toArgb()
                )
            v.findViewById<LinearLayout>(R.id.right_chat_container)?.backgroundTintList =
                ColorStateList.valueOf(
                    backgroundRightChat.toArgb()
                )

            // Khi click vào rightImageView thì hiển thị dialog
            rightImageView.setOnClickListener {
                if (messageList[adapterPosition].image != null) {
                    showImageDialog(it, messageList[adapterPosition].image!!)
                }
            }
            rightTextView.setTextColor(textRightColor.toArgb())

            // Khởi tạo WebView
            setupWebView(leftTextView)
            leftTextView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    // Enable JavaScript interface after page is loaded
                    isOnPageFinished = true
                }
            }

            copyText.setOnClickListener {
                val message = if (adapterPosition != RecyclerView.NO_POSITION) {
                    messageList[adapterPosition].message
                } else {
                    null
                }

                if (!message.isNullOrEmpty()) {
                    val clipboard =
                        v.context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clip =
                        ClipData.newPlainText(v.context.getString(R.string.copied_text), message)
                    clipboard.setPrimaryClip(clip)

                    Toast.makeText(
                        v.context,
                        v.context.getString(R.string.copied_to_clipboard), Toast.LENGTH_SHORT
                    ).show()
                } else {
                    Toast.makeText(
                        v.context,
                        v.context.getString(R.string.nothing_to_copy), Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }

        fun bind(message: Message) {
            if (message.image != null) {
                rightCardView.visibility = View.VISIBLE
                rightImageView.setImageBitmap(message.image)
            } else {
                rightCardView.visibility = View.GONE
            }
            if (message.sentBy == Message.SENT_BY_ME || message.sentBy == Message.SENT_BY_ME_IMG) {
                leftChatView.visibility = View.GONE
                leftChatModel.visibility = View.GONE
                rightChatView.visibility = View.VISIBLE
                if (message.message.isNotEmpty()) {
                    rightTextView.visibility = View.VISIBLE
                    rightTextView.text = message.message
                } else {
                    rightTextView.visibility = View.GONE
                }
            } else {
                leftChatView.visibility = View.VISIBLE
                leftChatModel.visibility = View.VISIBLE
                rightChatView.visibility = View.GONE

                leftChatModel.setImageResource(
                    when (message.sentBy) {
                        Message.SENT_BY_BOT_GPT -> R.drawable.ic_chat_gpt
                        Message.SENT_BY_BOT_GEMINI -> R.drawable.ic_gemini
                        else -> R.drawable.ic_chat_gpt
                    }
                )

//                aiName.text = when (message.sentBy) {
//                    Message.SENT_BY_BOT_GPT -> Const.AiServiceName.GPT
//                    Message.SENT_BY_BOT_GEMINI -> Const.AiServiceName.GEMINI
//                    else -> Const.AiServiceName.DEFAULT
//                }

                if (message.isError) {
                    animationView.visibility = View.GONE
                    animationView.cancelAnimation()
                    leftTextView.visibility = View.GONE
                    copyText.visibility = View.GONE
                    leftTextError.visibility = View.VISIBLE

                    resendIcon.visibility = View.VISIBLE
                    if (message.message.isEmpty())
                        leftTextError.text =
                            CommApplication.appContext.getString(R.string.errors_please_try_again)
                    else leftTextError.text = message.message
                    if (adapterPosition == messageList.size - 1) {
                        resendIcon.visibility = View.VISIBLE
                        resendIcon.setOnClickListener {
                            val lastUserMessage = getLastUserMessageBeforeError(
                                messageList,
                                messageList[adapterPosition]
                            )
                            if (lastUserMessage != null) {
                                onResendMessage(lastUserMessage)
                            }
                        }
                    } else {
                        resendIcon.visibility = View.GONE
                    }
                } else {
                    leftTextError.visibility = View.GONE
                    resendIcon.visibility = View.GONE

                    if (message.message.isEmpty()) {
                        animationView.visibility = View.VISIBLE
                        animationView.playAnimation()
                        leftTextView.visibility = View.GONE
                        copyText.visibility = View.GONE
                    } else {
                        animationView.visibility = View.GONE
                        animationView.cancelAnimation()
                        leftTextView.visibility = View.VISIBLE
                        copyText.visibility = View.VISIBLE

                        loadBaseHtml(leftTextView, message.message)
                    }
                }
            }
        }

        private fun setupWebView(webView: WebView) {
            webView.settings.javaScriptEnabled = true
            webView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
            webView.isVerticalScrollBarEnabled = false
            webView.isHorizontalScrollBarEnabled = false

            // Đọc file HTML từ assets
            val textColor = String.format("#%06X", 0xFFFFFF and textLeftColor.toArgb())
            val htmlContent = webView.context.assets.open("chat_webview_template.html")
                .bufferedReader()
                .use { it.readText() }
                .replace("\${textColor}", textColor)

            webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
        }

        @SuppressLint("JavascriptInterface")
        private fun injectContent(webView: WebView, content: String) {
            debugLog("injectContent: $content")
            if (content.isEmpty()) return
            val escapedContent = content
                .replace("\\", "\\\\") // Escape backslash
                .replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t")

            debugLog("escapedContent: $escapedContent")

            val javascript = """
            javascript:(function() {
                document.body.innerHTML = '$escapedContent';
                if (typeof renderMathInElement !== 'undefined') {
                    renderMathInElement(document.body, {
                        delimiters: [
                              {left: "\[", right: "\]", display: true},
                              {left: "\(", right: "\)", display: false},
                              {left: "$$", right: "$$", display: true},
                              {left: "$", right: "$", display: false},
                              {left: "\begin{equation}", right: "\end{equation}", display: true},
                              {left: "\begin{align}", right: "\end{align}", display: true},
                              {left: "\begin{alignat}", right: "\end{alignat}", display: true},
                              {left: "\begin{gather}", right: "\end{gather}", display: true},
                              {left: "\begin{CD}", right: "\end{CD}", display: true},
                        ],
                        throwOnError: false,
                        strict: false
                    });
                }
            })()
        """.trimIndent()

            webView.loadUrl(javascript)
        }

        @SuppressLint("SetJavaScriptEnabled")
        fun loadBaseHtml(webView: WebView, content: String = "") {
            val processedContent = preprocessFullLatex(content)

            if (CommFigs.IS_DEBUG)
                debugLog("loadBaseHtml: $processedContent")

            val html = processedContent.renderMarkdown()
            if (isOnPageFinished) {
                injectContent(webView, html)
            } else {
                webView.webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        isOnPageFinished = true
                        injectContent(webView, html)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val chatView =
            LayoutInflater.from(parent.context).inflate(R.layout.chat_item, parent, false)
        return MessageViewHolder(chatView)
    }

    override fun getItemCount(): Int = messageList.size

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        holder.bind(messageList[position])
    }

    fun addMessage(message: Message) {
        messageList.add(message) // Thêm tin nhắn mới vào danh sách
        notifyItemInserted(messageList.size - 1) // Thông báo RecyclerView rằng một tin nhắn mới đã được thêm
    }

    fun updateMessages(newMessages: List<Message>) {
        val diffResult = DiffUtil.calculateDiff(MessageDiffCallback(messageList, newMessages))
        messageList.clear()
        messageList.addAll(newMessages)
        diffResult.dispatchUpdatesTo(this)
    }

    fun updateLastChat(message: Message) {
        if (messageList.isNotEmpty()) {
            messageList[messageList.size - 1] = message
            notifyItemChanged(messageList.size - 1)
        }
    }

    private fun showImageDialog(view: View, imageBitmap: Bitmap) {
        val context = view.context
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_image_view, null)
        val imageView = dialogView.findViewById<PhotoView>(R.id.dialog_image)

        imageView.setImageBitmap(imageBitmap)

        val dialogBuilder = AlertDialog.Builder(context)
            .setView(dialogView)
            .setPositiveButton(context.getString(R.string.txtid_close)) { dialog, _ ->
                dialog.dismiss()
            }

        val dialog = dialogBuilder.create()
        dialog.show()
    }

    private fun getLastUserMessage(messages: List<Message>): Message? {
        return messages.lastOrNull { it.sentBy == Message.SENT_BY_ME }
    }

    private fun getLastUserMessageBeforeError(
        messages: List<Message>,
        errorMessage: Message
    ): Message? {
        val errorIndex = messages.indexOf(errorMessage)
        if (errorIndex > 0) {
            for (i in errorIndex - 1 downTo 0) {
                if (messages[i].sentBy == Message.SENT_BY_ME) {
                    return messages[i]
                }
            }
        }
        return null
    }
}

fun preprocessFullLatex(content: String): String {
    val testString = """
        \[
        \det
        \begin{bmatrix}
        a & b \\
        c & d
        \end{bmatrix}
        = ad - bc
        \]
    """.trimIndent()
    // Xử lý các dấu ngoặc LaTeX
    val step1 = addMissingLatexCloseBracket(content)
    val step2 = addMissingLatexCloseBracket2(step1)
    // Chuyển đổi các ký hiệu LaTeX sang định dạng phù hợp
    return step2
//        .replace("\\[", "$$")
//        .replace("\\]", "$$")
//        .replace("\\(", "$")
//        .replace("\\)", "$")
//        .replace("\\begin{align*}", "\\begin{align}")
//        .replace("\\end{align*}", "\\end{align}")
//        .replace("\\begin{eqnarray}", "\\begin{align}")
//        .replace("\\end{eqnarray}", "\\end{align}")
}

fun addMissingLatexCloseBracket(text: String): String {
    val lines = text.lines()
    val result = mutableListOf<String>()
    var insideLatex = false
    var buffer = mutableListOf<String>()

    for ((index, line) in lines.withIndex()) {
        val isBlankLine = line.isBlank()

        if (line.trim().startsWith("\\[")) {
            insideLatex = true
            buffer.add(line)
        } else if (insideLatex) {
            buffer.add(line)

            // Nếu dòng hiện tại là dòng trắng, tức là kết thúc khối mà chưa có \]
            if (isBlankLine) {
                val hasClosing = buffer.any { it.contains("\\]") }
                if (!hasClosing) {
                    // Thêm \] ngay trước dòng trắng
                    buffer.add(buffer.size - 1, "\\]")
                }
                result.addAll(buffer)
                buffer.clear()
                insideLatex = false
            }
        } else {
            result.add(line)
        }
    }

    // Trường hợp kết thúc file mà vẫn còn khối chưa đóng
    if (insideLatex) {
        val hasClosing = buffer.any { it.contains("\\]") }
        if (!hasClosing) {
            buffer.add("\\]")
        }
        result.addAll(buffer)
    }

    return result.joinToString("\n")
}

fun addMissingLatexCloseBracket2(text: String): String {
    val lines = text.lines()
    val result = mutableListOf<String>()
    var insideLatex = false
    var buffer = mutableListOf<String>()

    for ((index, line) in lines.withIndex()) {
        val isBlankLine = line.isBlank()

        if (line.trim().startsWith("\\(")) {
            insideLatex = true
            buffer.add(line)
        } else if (insideLatex) {
            buffer.add(line)

            // Nếu dòng hiện tại là dòng trắng, tức là kết thúc khối mà chưa có \]
            if (isBlankLine) {
                val hasClosing = buffer.any { it.contains("\\)") }
                if (!hasClosing) {
                    // Thêm \] ngay trước dòng trắng
                    buffer.add(buffer.size - 1, "\\)")
                }
                result.addAll(buffer)
                buffer.clear()
                insideLatex = false
            }
        } else {
            result.add(line)
        }
    }

    // Trường hợp kết thúc file mà vẫn còn khối chưa đóng
    if (insideLatex) {
        val hasClosing = buffer.any { it.contains("\\)") }
        if (!hasClosing) {
            buffer.add("\\)")
        }
        result.addAll(buffer)
    }

    return result.joinToString("\n")
}


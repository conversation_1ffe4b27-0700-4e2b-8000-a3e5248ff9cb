package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.advertisements.reward_ad.AdvertsManagerReward
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import amobi.module.compose.extentions.clipCorner
import amobi.module.compose.extentions.conditional
import amobi.module.compose.extentions.minSize
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppButtonText
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.AdIds
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainLogicViewModel
import com.amobilab.ezmath.ai.presentation.ui.iap.IapViewModel
import com.amobilab.ezmath.ai.presentation.ui.iap.SelectionState
import com.amobilab.ezmath.ai.presentation.ui.splash.SplashScreenCompose
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.delay
import java.util.Locale
import kotlin.math.max

@Composable
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
fun MainEntryCompose(appNavigator: AppNavigator) {
    val context = LocalContext.current
    val mainDataViewModel = hiltViewModel<MainDataViewModel>()
    val mainLogicViewModel = MainLogicViewModel.getInstance()

    val coinTotal = mainDataViewModel.coinViewModel.coinTotal.collectAsState().value
    val coinTotalOld = remember { mutableLongStateOf(coinTotal) }

    val freeChat = mainDataViewModel.coinViewModel.freeChat.collectAsState().value

    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = mainDataViewModel
        )
    }

    val navController = rememberNavController()
    appNavigator.setupWithNavController(
        activity = LocalContext.current as MainComposeActivity,
        navController = navController
    )

    val gradientBrush = Brush.linearGradient(
        colors = listOf(Color(0xFF81D8D0), Color(0xFF0ABAB5)), // Gradient màu mong muốn
        start = Offset(0f, 0f),
        end = Offset(0f, 100f)
    )


    val viewModelLap = hiltViewModel<IapViewModel>()
    val buy5500CoinProduct
            by viewModelLap.buy5500CoinProduct.observeAsState(initial = null)
    LaunchedEffect(Unit) {
        viewModel.fetchProduct()
    }

    LaunchedEffect(freeChat) {
        FirestoreUtils.updateFreeChatUsage(freeChat)
    }

    LaunchedEffect(coinTotal) {
        if (!googleAuthUiClient.checkAccountValidity()) {
            googleAuthUiClient.signOut()
        }
        if (coinTotal == coinTotalOld.longValue) return@LaunchedEffect
        googleAuthUiClient.getSignedInUser()?.let {
            if (coinTotal == 0L) return@let
            FirestoreUtils.updateCoinsForUser(it.userId, coinTotal)
        }

        val difference = coinTotal - coinTotalOld.longValue
        val message = if (difference > 0) "+$difference" else difference.toString()
        if (!CommFigs.IS_PRODUCT && difference < 0)
            MixedUtils.showToast(context, context.getString(R.string.txtid_coins) + ": $message")

        coinTotalOld.longValue = coinTotal
    }


//    val backStackEntryState by navController.currentBackStackEntryAsState()
//    val currentRouteName = backStackEntryState
//        ?.destination
//        ?.route
//        ?.substringAfterLast(".")
//        ?.substringBefore("/")
//        ?.split("?")
//        ?.first()


    val startDestination = remember {
        if (PrefAssist.getBoolean(PrefConst.ONBOARDING_COMPLETED)) {
            ScreenRoutes.HomeScreen()
        } else {
            ScreenRoutes.OnboardingScreen()
        }
    }

    val timeSplash =
        remember {
            mutableLongStateOf(
                max(RconfAssist.getInt(RconfConst.SPLASH_TIME_OUT), 2) * CommFigs.MILLIS_SECOND
            )
        }
    val isShowSplash = MainLogicViewModel.getInstance().isShowSplash.observeAsState().value

    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { innerPadding ->
        MainNavigationHost(
            navController = navController,
            startDestination = startDestination,
            innerPadding = innerPadding,
        )

        LaunchedEffect(key1 = timeSplash) {
            while (isShowSplash == true && timeSplash.longValue > 0) {
                val timeWait = 500L
                delay(timeWait)
                // Only decrease the timer if GDPR is not showing or the timer is below 500ms
                if (mainLogicViewModel.isShowGDPR() && timeSplash.longValue > timeWait * 2) {
                    timeSplash.longValue -= timeWait
                } else if (!mainLogicViewModel.isShowGDPR()) {
                    timeSplash.longValue -= timeWait
                }
                mainLogicViewModel.setShowSplash(timeSplash.longValue > 0)
            }
        }

        if (isShowSplash == true)
            SplashScreenCompose()

//            if (
//                currentRouteName == ScreenRoutes.HomeScreen().route
//                || currentRouteName == ScreenRoutes.ChatScreen().route
//                || currentRouteName == ScreenRoutes.CoinHistory().route
//                || currentRouteName == ScreenRoutes.InAppPurchaseRoute().route
//                || currentRouteName == ScreenRoutes.ScanDetail().route
//            ) {
//                AppRowCentered(
//                    modifier = Modifier
//                        .padding(innerPadding)
//                        .minWidth(AppSize.MIN_TOUCH_SIZE)
//                        .height(AppSize.APPBAR_HEIGHT)
//                        .align(Alignment.TopEnd)
//                        .appClickable {
//                            if (!RconfAssist.getBoolean(RconfConst.IS_SHOW_IAP))
//                                return@appClickable
//                            if (currentRouteName == ScreenRoutes.InAppPurchaseRoute().route)
//                                return@appClickable
//                            appNavigator.navigateTo(ScreenRoutes.InAppPurchaseRoute())
//                        },
//                ) {
//                    // Số coin
//                    AppIcon(
//                        R.drawable.svg_ic_coin,
//                        iconSize = 14.dp,
//                        color = Color.Yellow,
//                    )
//                    AppSpacer(4.dp)
//                    AppText(
//                        fontSize = 12.5.sp,
//                        text =
//                        if (currentRouteName == ScreenRoutes.InAppPurchaseRoute().route) {
//                            String.format(Locale.getDefault(), "%,d", coinTotal)
//                        } else {
//                            if (coinTotal > 1000000)
//                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000000).roundToLong())}M"
//                            else if (coinTotal > 10000)
//                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000).roundToLong())}K"
//                            else String.format(Locale.getDefault(), "%,d", coinTotal)
//                        },
//                        color = Color.Yellow
//                    )
//
//                }
//            }
    }

    if (mainDataViewModel.coinViewModel.isShowCoinInsufficientDialog.collectAsState().value) {
        @Composable
        fun ButtonRewardAd() {
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        AppColors.current.buttonInactiveGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .border(
                        width = 1.dp,
                        color = AppColors.current.borderColorButtonGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(8.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppGlideImage(
                        resId = R.drawable.ic_multi_coin,
                        modifier = Modifier
                            .size(40.dp),
                    )
                    AppSpacer(8.dp)
                    AppColumn(
                        modifier = Modifier.weight(1f)
                    ) {
                        AppText(
                            text = stringResource(
                                R.string.get_number_coins,
                                String.format(
                                    Locale.getDefault(),
                                    "%,d",
                                    RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD)
                                )
                            ),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W700,
                            color = AppColors.current.buttonColor,
                            lineHeight = 20.sp
                        )
                        AppText(
                            text = stringResource(R.string.quick_easy_no_payment_needed),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.Normal,
                            color = Color.White,
                            lineHeight = 20.sp
                        )
                    }
                    AppSpacer(4.dp)
                    AppRow(
                        modifier = Modifier
                            .background(
                                color = Color(0xFFFEF3B4),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .border(
                                width = 1.dp,
                                color = Color(0xFFFFEF97),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .clickable {
                                if (!AdvertsManagerReward.checkRewardAdverts(AdvertsManagerReward.rewardAds)) {
                                    MixedUtils.showToast(context, R.string.reward_ad_not_ready)
                                    AdvertsManagerReward.requestRewardAdverts(AdIds.REWARD_CREDIT)
                                    return@clickable
                                }
                                AdvertsManagerReward.showRewardAd(
                                    (context as CommActivity),
                                    AdvertsManagerReward.rewardAds,
                                    onUserEarnedReward = {
                                        val coinNumber =
                                            RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD)
                                                .toLong()
                                        val coinString =
                                            String.format(Locale.getDefault(), "%,d", coinNumber)
                                        mainDataViewModel.coinViewModel.updateCoinBalance(
                                            coinNumber,
                                            context.getString(R.string.txtid_watch_reward_ad)
                                        )
                                        MixedUtils.showToast(
                                            context,
                                            context.getString(R.string.txtid_coins) + ": +$coinString"
                                        )
                                    },
                                    onShowAdCompleteListener = {
                                        mainDataViewModel.coinViewModel.hideInsufficientCoinDialog()
                                    }
                                )
                            }
                            .padding(horizontal = 8.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        AppText(
                            text = stringResource(R.string.txtid_free),
                            fontSize = AppFontSize.BODY2,
                            color = AppColors.current.moneyTextColor,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            lineHeight = 20.sp
                        )
                        AppSpacer(4.dp)
                        AppIcon(
                            R.drawable.ic_media,
                            size = 20.dp,
                            tint = null,
                        )
                    }
                }

            }
        }

        @Composable
        fun ButtonGetCoins() {
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        AppColors.current.buttonInactiveGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .border(
                        width = 1.dp,
                        color = AppColors.current.borderColorButtonGetCredits,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .padding(8.dp)
            ) {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppGlideImage(
                        resId = R.drawable.ic_multi_coin,
                        modifier = Modifier
                            .size(40.dp),
                    )
                    AppSpacer(8.dp)
                    AppColumn(
                        modifier = Modifier.weight(1f)
                    ) {
                        AppText(
                            text = stringResource(R.string.unlock_chat_access_instanly),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W700,
                            color = AppColors.current.buttonColor,
                            lineHeight = 20.sp
                        )
                        AppText(
                            text = "Buy Credits today – 20% OFF, starting from just ${buy5500CoinProduct?.oneTimePurchaseOfferDetails?.formattedPrice ?: ""}! Limited-time deal!",
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.Normal,
                            color = Color.White,
                            lineHeight = 20.sp
                        )
                    }
                }

            }
        }

        Dialog(
            onDismissRequest = {
                mainDataViewModel.coinViewModel.hideInsufficientCoinDialog()
            }
        ) {
            AppBox(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp))
                    .background(
                        color = MaterialTheme.colorScheme.background,
                        shape = RoundedCornerShape(16.dp)
                    )
            ) {
                // background
                Image(
                    painter = painterResource(id = R.drawable.bg_get_credits_new),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .matchParentSize()
                )


                AppColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 24.dp)
                ) {

                    AppRow(
                        modifier = Modifier.fillMaxWidth().padding(start = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        AppText(
                            modifier = Modifier.weight(1f),
                            text = "🌟 " + stringResource(R.string.out_of_credits),
                            fontSize = AppFontSize.PLUS_SIZE,
                            fontWeight = FontWeight.W700,
                            color = Color.White,
                            lineHeight = 32.sp
                        )
                        //icon close
                        AppIcon(
                            imageVector = ImageVector.vectorResource(R.drawable.ic_close),
                            clickZone = AppSize.MIN_TOUCH_SIZE,
                        ) {
                            mainDataViewModel.coinViewModel.hideInsufficientCoinDialog()
                        }

                    }

                    AppSpacer(12.dp)

                    AppColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    ) {
                        AppText(
                            text = stringResource(R.string.you_ve_used_up_all_your_credits_let_s_get_you_chatting_again),
                            fontSize = AppFontSize.BODY1,
                            fontWeight = FontWeight.W400,
                            lineHeight = 24.sp,
                            color = Color.White
                        )

                        AppSpacer(24.dp)

                        AppText(
                            modifier = Modifier.padding(start = 12.dp),
                            text = stringResource(R.string.watch_an_ad).uppercase(),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor
                        )

                        AppSpacer(4.dp)

                        ButtonRewardAd()

                        AppSpacer(16.dp)

                        AppText(
                            modifier = Modifier.padding(start = 12.dp),
                            text = stringResource(R.string.or).uppercase(),
                            fontSize = AppFontSize.BODY2,
                            fontWeight = FontWeight.W400,
                            color = AppColors.current.textHintColor
                        )

                        AppSpacer(4.dp)

                        ButtonGetCoins()

                        AppSpacer(24.dp)

                        AppButton(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(48.dp),
                            onClick = {
                                mainDataViewModel.coinViewModel.hideInsufficientCoinDialog()
                                appNavigator.navigateTo(ScreenRoutes.InAppPurchaseRoute())
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Transparent,
                                contentColor = AppColors.current.onText
                            ),
                            shape = RoundedCornerShape(8.dp),
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        brush = gradientBrush,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .padding(horizontal = 16.dp, vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                AppText(
                                    text = stringResource(R.string.buy_coins),
                                    fontSize = AppFontSize.BODY1,
                                    fontWeight = FontWeight.W500,
                                    color = AppColors.current.onText,
                                    lineHeight = 24.sp
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

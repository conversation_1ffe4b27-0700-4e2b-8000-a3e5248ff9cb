package com.amobilab.ezmath.ai.presentation.common.zoom_ui

import amobi.module.common.utils.debugLog
import amobi.module.compose.foundation.AppBox
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipRect
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun ArcZoomSlider(
    modifier: Modifier = Modifier,
    currentZoom: Float = 1.0f,
    maxZoom: Float = 10f,
    onZoomChange: (Float) -> Unit
) {
    val minZoom = 1.0f // Giá trị zoom tối thiểu (1x)
    val adjustedMaxZoom = if (maxZoom >= 10f) 10f else 5.1f

    val haptic = LocalHapticFeedback.current
    val triggeredHapticIndices = mutableSetOf<Int>()

    // Tạo danh sách các mức zoom từ minZoom đến maxZoom
    val zoomLevels = remember(adjustedMaxZoom) {
        val step = 0.1f
        val levels = mutableListOf<Float>()
        var value = minZoom
        while (value <= adjustedMaxZoom + 0.001f) {
            levels.add(((value * 10).toInt() / 10f))  // Làm tròn 1 chữ số thập phân
            value += step
        }
        levels
    }


    val angleRange = 180f
    val tickCount = zoomLevels.size - 1
    val tickAngleStep = angleRange / tickCount
    val startAngle = 270f

    // Tìm index gần nhất với currentZoom
    val closestIndex = remember(currentZoom) {
        var minIndex = 0
        var minDiff = Float.MAX_VALUE

        for (i in zoomLevels.indices) {
            val diff = abs(zoomLevels[i] - currentZoom)
            if (diff < minDiff) {
                minDiff = diff
                minIndex = i
            }
        }

        minIndex
    }

    var selectedIndex by remember(closestIndex) { mutableStateOf(closestIndex) }
    var dragStartX by remember { mutableStateOf<Float?>(null) }

    var isDraggingAllowed by remember { mutableStateOf(false) }

    AppBox(
        modifier = modifier
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(2f)
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            // CHỈ cho phép kéo nếu chạm vào nửa dưới của canvas
                            isDraggingAllowed = offset.y > size.height / 2
                            if (isDraggingAllowed) {
                                dragStartX = offset.x
                            }
                        },
                        onDrag = { change, _ ->
                            if (!isDraggingAllowed) return@detectDragGestures

                            dragStartX?.let { startX ->
                                val deltaX = change.position.x - startX
                                val threshold = 8f
                                if (abs(deltaX) > threshold) {
                                    val steps = (-deltaX / threshold).toInt()
                                    val newIndex = (selectedIndex + steps).coerceIn(0, tickCount)
                                    if (newIndex != selectedIndex) {
                                        selectedIndex = newIndex
                                        // Đảm bảo rằng giá trị zoom trả về chính xác
                                        onZoomChange(zoomLevels[selectedIndex])
                                        dragStartX = change.position.x
                                    }
                                }
                            }
                            change.consume()
                        },
                        onDragEnd = {
                            isDraggingAllowed = false
                            dragStartX = null
                        },
                        onDragCancel = {
                            isDraggingAllowed = false
                            dragStartX = null
                        }
                    )
                }
        ) {
            val radius = size.height
            val paddingPx = 8.dp.toPx()
            val effectiveRadius = radius - paddingPx
            val center = Offset(x = size.width / 2f, y = size.height + size.height / 2)

            // 🎨 NỀN nửa trên - dùng full radius (KHÔNG padding)
            clipRect(0f, 0f, size.width, size.height) {
                drawArc(
                    color = Color(0xFF000000).copy(alpha = 0.2f),
                    startAngle = 180f,
                    sweepAngle = 180f,
                    useCenter = true,
                    topLeft = Offset(center.x - radius, center.y - radius),
                    size = Size(radius * 2, radius * 2)
                )
            }

            // 🎯 Tick, mũi tên và text - dùng effectiveRadius (CÓ padding)
            clipRect(0f, 0f, size.width, size.height) {
                zoomLevels.forEachIndexed { index, zoomLevel ->
                    val angle = startAngle + (index - selectedIndex) * tickAngleStep
                    if (angle !in 180f..360f) return@forEachIndexed

                    val rad = Math.toRadians(angle.toDouble())
                    // Đánh dấu các mức zoom chính: 1x, 2x, 5x
                    val isMajorTick =
                        zoomLevel == 1f || zoomLevel == 2f || zoomLevel == 5f || zoomLevel == 10f
                    val shouldDrawText =
                        zoomLevel == 1f || zoomLevel == 2f || zoomLevel == 5f || zoomLevel == 10f
                    val tickLength =
                        if (isMajorTick) effectiveRadius * 0.1f else effectiveRadius * 0.05f
                    val strokeWidth =
                        if (isMajorTick) effectiveRadius * 0.015f else effectiveRadius * 0.01f

                    val baseColor = if (index == selectedIndex) Color.Yellow else Color.White
                    val color = if (isMajorTick) baseColor else baseColor.copy(alpha = 0.4f)

                    val start = Offset(
                        x = center.x + (effectiveRadius - tickLength) * cos(rad).toFloat(),
                        y = center.y + (effectiveRadius - tickLength) * sin(rad).toFloat()
                    )
                    val end = Offset(
                        x = center.x + effectiveRadius * cos(rad).toFloat(),
                        y = center.y + effectiveRadius * sin(rad).toFloat()
                    )

                    drawLine(
                        color = color,
                        start = start,
                        end = end,
                        strokeWidth = strokeWidth
                    )

                    // Vẽ số cho tick major
                    if (shouldDrawText) {
                        val textPaint = android.graphics.Paint().apply {
                            this.color = android.graphics.Color.WHITE
                            textAlign = android.graphics.Paint.Align.CENTER
                            textSize = effectiveRadius * 0.09f
                            isAntiAlias = true
                        }

                        val offsetDistance = effectiveRadius * 0.12f
                        val textOffset = Offset(
                            x = start.x - offsetDistance * cos(rad).toFloat(),
                            y = start.y - offsetDistance * sin(rad).toFloat()
                        )

                        val rotationAngle = angle + 90f

                        drawIntoCanvas { canvas ->
                            canvas.save()
                            canvas.translate(textOffset.x, textOffset.y)
                            canvas.rotate(rotationAngle)
                            // Hiển thị giá trị zoom đơn giản hóa trên các tick
                            val tickText = when (zoomLevel) {
                                1f -> "1x"
                                2f -> "2x"
                                5f -> "5x"
                                10f -> "10x"
                                else -> zoomLevel.toInt().toString()
                            }
                            canvas.nativeCanvas.drawText(
                                tickText,
                                0f,
                                0f,
                                textPaint
                            )
                            canvas.restore()
                        }
                    }

                }


                // Mũi tên
                val arrowPath = Path().apply {
                    val cornerRadius =
                        effectiveRadius * 0.01f * 0.6f  // 0.01f là gốc, giảm 40% còn 0.6

                    // Điều chỉnh vị trí lên cao hơn bằng cách thêm offset
                    val yOffset =
                        effectiveRadius * 0.025f  // Thêm offset để đẩy mũi tên lên cao hơn

                    // Đảo chiều mũi tên bằng cách đổi vị trí các điểm
                    val topPoint = Offset(
                        center.x,
                        center.y - effectiveRadius + effectiveRadius * 0.211f * 0.6f - yOffset
                    )
                    val leftPoint = Offset(
                        center.x - effectiveRadius * 0.025f * 0.6f,
                        center.y - effectiveRadius + effectiveRadius * 0.01f * 0.6f - yOffset
                    )
                    val rightPoint = Offset(
                        center.x + effectiveRadius * 0.025f * 0.6f,
                        center.y - effectiveRadius + effectiveRadius * 0.01f * 0.6f - yOffset
                    )

                    moveTo(leftPoint.x + cornerRadius, leftPoint.y)
                    quadraticTo(leftPoint.x, leftPoint.y, leftPoint.x, leftPoint.y + cornerRadius)
                    lineTo(topPoint.x - cornerRadius, topPoint.y)
                    quadraticTo(topPoint.x, topPoint.y, topPoint.x + cornerRadius, topPoint.y)
                    lineTo(rightPoint.x, rightPoint.y + cornerRadius)
                    quadraticTo(
                        rightPoint.x,
                        rightPoint.y,
                        rightPoint.x - cornerRadius,
                        rightPoint.y
                    )
                    close()
                }

                drawPath(arrowPath, Color(0xFFE6C083))

                // Text - Hiển thị giá trị zoom thực tế
                drawIntoCanvas { canvas ->
                    // Hiển thị giá trị zoom thực tế với định dạng "%.1fx"

                    val zoomValue = zoomLevels[selectedIndex]
                    val isMajorTick =
                        zoomValue == 1f || zoomValue == 2f || zoomValue == 5f || zoomValue == 10f

                    debugLog("selectedIndex: $selectedIndex, triggeredHapticIndices: $triggeredHapticIndices, isMajorTick: $isMajorTick")

                    if (isMajorTick && !triggeredHapticIndices.contains(selectedIndex)) {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        triggeredHapticIndices.add(selectedIndex)
                    }


                    val displayValue = String.format("%.1fx", zoomValue)

                    canvas.nativeCanvas.drawText(
                        displayValue,
                        center.x,
                        center.y - effectiveRadius * 0.6f,
                        android.graphics.Paint().apply {
                            textAlign = android.graphics.Paint.Align.CENTER
                            textSize = effectiveRadius * 0.1f
                            color = "#E6C083".toColorInt()
                            isAntiAlias = true
                            isFakeBoldText = true // Làm chữ đậm hơn
                        }
                    )
                }
            }
        }
    }
}

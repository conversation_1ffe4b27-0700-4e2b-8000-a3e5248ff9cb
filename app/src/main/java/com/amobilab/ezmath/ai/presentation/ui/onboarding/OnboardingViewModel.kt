package com.amobilab.ezmath.ai.presentation.ui.onboarding

import amobi.module.common.configs.PrefAssist
import androidx.lifecycle.ViewModel
import com.amobilab.ezmath.ai.data.pref.PrefConst
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class OnboardingViewModel @Inject constructor() : ViewModel() {
    
    fun setOnboardingCompleted() {
        PrefAssist.setBoolean(PrefConst.ONBOARDING_COMPLETED, true)
    }
    
    fun isOnboardingCompleted(): Boolean {
        return PrefAssist.getBoolean(PrefConst.ONBOARDING_COMPLETED, false)
    }
}

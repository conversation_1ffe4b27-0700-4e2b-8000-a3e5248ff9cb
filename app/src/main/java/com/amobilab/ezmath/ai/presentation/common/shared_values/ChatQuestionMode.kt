package com.amobilab.ezmath.ai.presentation.common.shared_values

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.Color
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.PromptSuggestion


enum class ChatQuestionMode(
    val id: String,
    @StringRes val titleId: Int,
    @StringRes val subtitleId: Int,
    val color: Color,
    @DrawableRes val iconId: Int,
    @StringRes val instructionId: Int,
    @StringRes val promptImageId: Int,
    val listSuggestions: List<PromptSuggestion>
) {
    Math(
        id = "Math",
        titleId = R.string.txtid_math,
        subtitleId = R.string.txtid_math_subtitle,
        color = Color(0.741f, 0.867f, 1.0f, 1.0f),
        iconId = R.drawable.ic_mode_math_chat,
        instructionId = R.string.question_math_system_instruction,
        promptImageId = R.string.question_math_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.equation,
                R.string.help_me_solve_this_equation_2x_3_11),
            PromptSuggestion(R.string.derivative,
                R.string.show_me_how_to_find_the_derivative_of_f_x_x_sin_x),
            PromptSuggestion(R.string.limit,
                R.string.find_the_limit_of_sin_x_x_as_x_approaches_0)
        )
    ),
    Translate(
        id = "Translate",
        titleId = R.string.txtid_translate,
        subtitleId = R.string.txtid_translate_subtitle,
        color = Color(0.863f, 0.863f, 0.863f, 1.0f),
        iconId = R.drawable.ic_mode_translate_chat,
        instructionId = R.string.question_translate_system_instruction,
        promptImageId = R.string.question_translate_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.english,
                R.string.translate_this_sentence_into_english_t_i_r_t_th_ch_h_c_ngo_i_ng),
            PromptSuggestion(R.string.meaning,
                R.string.what_does_the_word_serendipity_mean_in_vietnamese),
            PromptSuggestion(R.string.japanese,
                R.string.help_me_translate_this_paragraph_into_japanese)
        )
    ),
    Writing(
        id = "Writing",
        titleId = R.string.txtid_writing,
        subtitleId = R.string.txtid_writing_subtitle,
        color = Color(1.0f, 0.961f, 0.69f, 1.0f),
        iconId = R.drawable.ic_mode_writing_chat,
        instructionId = R.string.question_writing_system_instruction,
        promptImageId = R.string.question_writing_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(
                R.string.paragraph,
                R.string.write_a_short_paragraph_about_the_importance_of_sleep
            ),
            PromptSuggestion(
                R.string.email,
                R.string.help_me_write_a_professional_leave_request_email
            ),
            PromptSuggestion(
                R.string.intro,
                R.string.write_an_introduction_paragraph_for_an_essay_on_climate_change
            )
        )
    ),
    Chemistry(
        id = "Chemistry",
        titleId = R.string.chemistry,
        subtitleId = R.string.chemistry_subtitle,
        color = Color(1.0f, 0.816f, 0.855f, 1.0f),
        iconId = R.drawable.ic_mode_chemistry_chat,
        instructionId = R.string.question_chemistry_system_instruction,
        promptImageId = R.string.question_chemistry_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.neutralize,
                R.string.explain_what_an_acid_base_neutralization_reaction_is),
            PromptSuggestion(R.string.ethanol,
                R.string.what_is_the_structural_formula_of_ethanol),
            PromptSuggestion(R.string.bonds,
                R.string.what_is_the_difference_between_ionic_and_covalent_bonds)
        )
    ),
    Geography(
        id = "Geography",
        titleId = R.string.geography,
        subtitleId = R.string.geography_subtitle,
        color = Color(0.741f, 0.773f, 0.992f, 1.0f),
        iconId = R.drawable.ic_mode_geography_chat,
        instructionId = R.string.question_geography_system_instruction,
        promptImageId = R.string.question_geography_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.capital,
                R.string.what_is_the_capital_of_brazil_what_s_special_about_it),
            PromptSuggestion(R.string.compare,
                R.string.compare_the_geography_of_asia_and_africa),
            PromptSuggestion(R.string.desert,
                R.string.why_is_the_sahara_the_largest_desert_in_the_world)
        )
    ),
    Literature(
        id = "Literature",
        titleId = R.string.feature_card_literature,
        subtitleId = R.string.feature_card_literature,
        color = Color(0.741f, 0.867f, 1.0f, 1.0f),
        iconId = R.drawable.ic_mode_math_chat,
        instructionId = R.string.question_default_system_instruction,
        promptImageId = R.string.question_default_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.the_great_gatsby,
                R.string.write_about_the_great_gatsby),
            PromptSuggestion(R.string.character_analysis,
                R.string.do_character_analysis),
            PromptSuggestion(R.string.main_themes,
                R.string.analyze_main_themes)
        )
    ),
    ResearchAndAnalysis(
        id = "ResearchAndAnalysis",
        titleId = R.string.feature_card_research_and_analysis,
        subtitleId = R.string.feature_card_research_and_analysis,
        color = Color(0.741f, 0.867f, 1.0f, 1.0f),
        iconId = R.drawable.ic_mode_math_chat,
        instructionId = R.string.question_default_system_instruction,
        promptImageId = R.string.question_default_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.climate_change,
                R.string.research_on_climate_change),
            PromptSuggestion(R.string.info_gathering,
                R.string.goal_information_gathering),
            PromptSuggestion(R.string.scientific_journals,
                R.string.use_scientific_journals)
        )
    ),
    WriteAnEssay(
        id = "WriteAnEssay",
        titleId = R.string.feature_card_write_an_essay,
        subtitleId = R.string.feature_card_write_an_essay,
        color = Color(0.741f, 0.867f, 1.0f, 1.0f),
        iconId = R.drawable.ic_mode_math_chat,
        instructionId = R.string.question_default_system_instruction,
        promptImageId = R.string.question_default_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.describe_place,
                R.string.describe_a_place_that_makes_you_feel_at_peace),
            PromptSuggestion(R.string.argumentative,
                R.string.argumentative_essay_example),
            PromptSuggestion(R.string.word_500,
                R.string.write_essay_500_words)
        )
    ),
    Default(
        id = "Default",
        titleId = R.string.chat_bot,
        subtitleId = R.string.chat_bot,
        color = Color(0.741f, 0.867f, 1.0f, 1.0f),
        iconId = R.drawable.ic_mode_math_chat,
        instructionId = R.string.question_default_system_instruction,
        promptImageId = R.string.question_default_prompt_image,
        listSuggestions = listOf(
            PromptSuggestion(R.string.start,
                R.string.hi_what_can_you_help_me_with_today),
            PromptSuggestion(R.string.explain,
                R.string.can_you_explain_how_this_app_works),
            PromptSuggestion(R.string.tips,
                R.string.give_me_tips_for_getting_the_best_results)
        )
    )
}

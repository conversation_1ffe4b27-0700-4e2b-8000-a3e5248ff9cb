package com.amobilab.ezmath.ai.presentation.ui.chat_bot.components

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.amobilab.ezmath.ai.R

class ChatAvatarRoundedImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : AppCompatImageView(context, attrs, defStyleAttr) {
    private var radius = 20.0f // Bán kính của góc bo
    private val path = Path()
    private val rect = RectF()
    private val paint = Paint()

    init {
        paint.isAntiAlias = true
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 20f // <PERSON><PERSON> dày của viền
        paint.color = ContextCompat.getColor(context, R.color.colorPrimary) // <PERSON><PERSON>u sắc của viền
    }

    override fun onDraw(canvas: Canvas) {
        val drawable = drawable as? BitmapDrawable ?: return
        val bitmap = drawable.bitmap ?: return

        rect.set(0f, 0f, width.toFloat(), height.toFloat())
        path.addRoundRect(rect, radius, radius, Path.Direction.CW)
        canvas.clipPath(path)
        canvas.drawBitmap(bitmap, null, rect, null)
        canvas.drawRoundRect(rect, radius, radius, paint) // Vẽ viền bo góc
    }

    // Phương thức này để đặt bán kính của góc bo
    fun setRadius(radius: Float) {
        this.radius = radius
        invalidate()
    }
}
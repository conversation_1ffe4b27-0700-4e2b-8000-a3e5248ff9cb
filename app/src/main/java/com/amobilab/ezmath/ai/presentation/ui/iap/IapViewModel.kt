package com.amobilab.ezmath.ai.presentation.ui.iap

import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.BaseViewModel
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.navigation.MainComposeActivity
import com.amobilab.ezmath.ai.utils.IapAssist
import com.android.billingclient.api.ProductDetails
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class IapViewModel @Inject constructor() : BaseViewModel() {
    private var homeActivity: WeakReference<MainComposeActivity>? = null

    val buyCoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }
    val buy1500CoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }
    val buy5500CoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }
    val buy12000CoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }
    val buy25000CoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }
    val buy53000CoinProduct: MutableLiveData<ProductDetails> by lazy {
        MutableLiveData<ProductDetails>()
    }

    val iapProductFetching: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>(true)
    }

    fun fetchProduct() {
        iapProductFetching.value = true
        CoroutineScope(Dispatchers.IO).launch {
            do {
                if (!IapAssist.instance.isProductQuerying) {
                    val buyCoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COIN_PURCHASE
                    )
                    val buy1500CoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COINS_PURCHASE_1500
                    )
                    val buy5500CoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COINS_PURCHASE_5500
                    )
                    val buy12000CoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COINS_PURCHASE_12000
                    )
                    val buy25000CoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COINS_PURCHASE_25000
                    )
                    val buy53000CoinProduct = IapAssist.instance.getQueriedProduct(
                        IapAssist.Products.COINS_PURCHASE_53000
                    )

                    withContext(Dispatchers.Main) {
                        <EMAIL> = buyCoinProduct
                        <EMAIL> = buy1500CoinProduct
                        <EMAIL> = buy5500CoinProduct
                        <EMAIL> = buy12000CoinProduct
                        <EMAIL> = buy25000CoinProduct
                        <EMAIL> = buy53000CoinProduct
                        <EMAIL> = false
                    }
                }
                delay(500)
            } while (IapAssist.instance.isProductQuerying)
        }
    }

    fun startPurchaseIAP(context: Context, product: ProductDetails?) {
        homeActivity = WeakReference(context as MainComposeActivity)

//        if (CommFigs.IS_DEBUG) {
//            MixedUtils.showToast(context, R.string.purchase_successful)
//            val coinNumber = when (product!!.productId) {
//                buy1500CoinProduct.value?.productId -> 1500000
//                buy5500CoinProduct.value?.productId -> 5500000
//                buy12000CoinProduct.value?.productId -> 12000000
//                buy25000CoinProduct.value?.productId -> 25000000
//                buy53000CoinProduct.value?.productId -> 53000000
//                else -> 0
//            }
//            coinViewModel.updateCoinBalance(
//                coinNumber,
//                context.getString(R.string.purchased_num_coins, String.format(Locale.getDefault(), "%,d", coinNumber))
//            )
//        }

        IapAssist.instance.purchaseConsumableProduct(
            homeActivity?.get() ?: return,
            product?.productId ?: return,
            onSuccess = {
                MixedUtils.showToast(context, R.string.purchase_successful)
                val coinNumber = when (product.productId) {
                    buy1500CoinProduct.value?.productId -> RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_1500)
                    buy5500CoinProduct.value?.productId -> RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_5500)
                    buy12000CoinProduct.value?.productId -> RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_12000)
                    buy25000CoinProduct.value?.productId -> RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_25000)
                    buy53000CoinProduct.value?.productId -> RconfAssist.getLong(RconfConst.NUM_COINS_PURCHASE_53000)
                    else -> 0
                }
                coinViewModel.updateCoinBalance(
                    coinNumber,
                    context.getString(R.string.purchased_num_coins, String.format(Locale.getDefault(), "%,d", coinNumber))
                )

            },
            onError = { billingResult ->
                MixedUtils.showToast(context, R.string.purchase_failed)
            }
        )
    }

    fun onRewardAdComplete(context: Context) {
        val coinNumber = RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD).toLong()
        val coinString = String.format(Locale.getDefault(), "%,d", coinNumber)
        coinViewModel.updateCoinBalance(
            coinNumber,
            context.getString(R.string.txtid_watch_reward_ad)
        )
        MixedUtils.showToast(context, context.getString(R.string.txtid_coins) + ": +$coinString")
    }
}
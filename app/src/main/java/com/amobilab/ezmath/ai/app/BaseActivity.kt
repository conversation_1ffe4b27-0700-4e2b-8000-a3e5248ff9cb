package com.amobilab.ezmath.ai.app

import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist.initPreviewPreferences
import amobi.module.common.utils.dlog
import amobi.module.common.views.CommActivity
import amobi.module.compose.theme.AppThemeWrapper
import android.content.pm.ActivityInfo
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
abstract class BaseActivity : CommActivity() {
    @Inject
    lateinit var coinViewModel: CoinViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (CommFigs.IS_DEBUG)
            dlog("Compact Screen: ${isScreenCompact()}")

        requestedOrientation = if (isScreenCompact())
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT else
            ActivityInfo.SCREEN_ORIENTATION_FULL_USER

        initPreviewPreferences(this)
        setContent {
            val appThemeMode = coinViewModel.appThemeMode.observeAsState()

            AppThemeWrapper(
                darkTheme = when (appThemeMode.value) {
                    AppThemeMode.DARK -> true
                    AppThemeMode.LIGHT -> false
                    AppThemeMode.SYSTEM -> isSystemInDarkTheme()
                    null -> isSystemInDarkTheme()
                }
            ) {
                MainContentCompose()
            }
        }

        // make full transparent statusBar // TODO monitor ANRs tssst
//        val visibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//        window.decorView.systemUiVisibility = visibility
//        val windowManager =
//            WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
//        setWindowFlag(windowManager, false)

        enableEdgeToEdge()

//        BarUtils.setStatusBarLightMode(this, false) // TODO monitor ANRs tssst
    }

    @Composable
    protected abstract fun MainContentCompose()

    private fun setWindowFlag(bits: Int, on: Boolean) {
        val winParams = window.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        window.attributes = winParams
    }
}
package com.amobilab.ezmath.ai.presentation.ui.calculator

import amobi.module.compose.foundation.AppColumn
import android.annotation.SuppressLint
import android.webkit.ConsoleMessage
import android.webkit.WebChromeClient
import android.webkit.WebView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun CalculatorCompose() {
    val navigatorViewModel = NavigatorViewModel.getInstance()

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
            AppAppbar(
                title = stringResource(R.string.txtid_calculator),
                innerPadding = innerPadding,
                onBack = {
                    navigatorViewModel.navigateBack()
                },
            )
            AndroidView(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = Color(0xFFf1f1f1))
                    .padding(bottom = innerPadding.calculateBottomPadding() + 16.dp),
                factory = { context ->
                    WebView(context).apply {
                        webChromeClient = object : WebChromeClient() {
                            override fun onConsoleMessage(
                                consoleMessage: ConsoleMessage
                            ): Boolean {
                                return true
                            }
                        }
                        settings.apply {
                            javaScriptEnabled = true
                            // Thêm các cài đặt khác nếu cần
                        }
                        isFocusable = true
                        isFocusableInTouchMode = true
                        loadUrl("file:///android_asset/guppy/keyboard.html")
                    }
                },
            )
        }
    }

}
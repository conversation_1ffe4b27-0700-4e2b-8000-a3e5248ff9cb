<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">रूपरेखा बनाएं</string>
    <string name="placeholder_default" translate_by="gemini">यहां अपना टेक्स्ट दर्ज करें…</string>
    <string name="essay_screen_description" translate_by="gemini">ज़रूर! मैं इसमें मदद कर सकता हूँ। कृपया नीचे अपने निबंध के लिए कुछ विवरण प्रदान करें।</string>
    <string name="label_choose_topic" translate_by="gemini">एक विषय चुनें</string>
    <string name="label_essay_type" translate_by="gpt">निबंध का प्रकार</string>
    <string name="label_word_count" translate_by="gpt">शब्द गणना</string>
    <string name="label_language_tone" translate_by="gemini">भाषा + टोन</string>
    <string name="placeholder_topic" translate_by="gpt">एक ऐसी जगह का वर्णन करें जो आपको शांति का अनुभव कराती है।</string>
    <string name="placeholder_essay_type" translate_by="gemini">उदाहरण के तौर पर: विवादात्मक, वर्णनात्मक…</string>
    <string name="placeholder_word_count" translate_by="gpt">उदाहरण: 300 शब्द, 500 शब्द, 1000 शब्द…</string>
    <string name="placeholder_language_tone" translate_by="gemini">औपचारिक, अकादमिक, …</string>
    <string name="research_screen_description" translate_by="google">अनुसंधान और विश्लेषण के माध्यम से कच्चे डेटा को सार्थक, दृश्य कहानियों में बदलने के लिए एक जगह।</string>
    <string name="label_research_topic" translate_by="gpt">अनुसंधान विषय</string>
    <string name="label_research_goal" translate_by="gpt">अनुसंधान लक्ष्य</string>
    <string name="label_preferred_sources" translate_by="gpt">पसंदीदा स्रोत</string>
    <string name="label_depth_length" translate_by="gpt">गहराई / लंबाई</string>
    <string name="label_academic_level" translate_by="gpt">शैक्षणिक स्तर</string>
    <string name="placeholder_research_topic" translate_by="gpt">जलवायु परिवर्तन, एआई का नौकरियों पर प्रभाव, …</string>
    <string name="placeholder_research_goal" translate_by="gemini">जानकारी जुटाना, रुझान विश्लेषण…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">वैज्ञानिक पत्रिकाएँ, पुस्तकें, आधिकारिक लेख</string>
    <string name="placeholder_depth_length" translate_by="gpt">उदाहरण: 300 शब्द, 500 शब्द, 1000 शब्द…</string>
    <string name="placeholder_academic_level" translate_by="gemini">हाई स्कूल के छात्र, कॉलेज के छात्र, उन्नत अनुसंधान, …</string>
    <string name="literature_screen_description" translate_by="gpt">शब्दों से छिपे अर्थों तक, हम आपको हर साहित्यिक कार्य का असली मूल्य उजागर करने में मदद करते हैं।</string>
    <string name="label_title_of_work" translate_by="gpt">कार्य का शीर्षक</string>
    <string name="label_author" translate_by="gpt">लेखक</string>
    <string name="label_analysis_type" translate_by="gpt">आप क्या विश्लेषण करना चाहते हैं?</string>
    <string name="label_format" translate_by="gpt">लंबाई / प्रारूप</string>
    <string name="placeholder_title" translate_by="gpt">महान गैट्सबी</string>
    <string name="placeholder_author" translate_by="gpt">एफ. स्कॉट फ़िज़्ज़राल्ड</string>
    <string name="placeholder_analysis_type" translate_by="gemini">चरित्र विश्लेषण, मुख्य विषय…</string>
    <string name="placeholder_format" translate_by="gpt">उदाहरण: 300 शब्द, 500 शब्द, 1000 शब्द…</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">मध्य विद्यालय, उच्च विद्यालय या विश्वविद्यालय, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 अनुसंधान विषय: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 अनुसंधान लक्ष्य: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 पसंदीदा स्रोत: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 गहराई/लंबाई: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 शैक्षणिक स्तर: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="google">🧾 सुझाई गई रूपरेखा:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. परिचय</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- %1$s का संक्षिप्त अवलोकन</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">%1$s स्तर पर अनुसंधान का महत्व</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. उद्देश्य</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">प्राथमिक लक्ष्य स्पष्ट करें: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gemini">3. कार्यप्रणाली</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- अनुसंधान दृष्टिकोण</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- डेटा स्रोत: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. मुख्य अंतर्दृष्टियाँ</string>
    <string name="research_outline_key_insights_trends" translate_by="google">- रुझानों, तथ्यों या विश्लेषण निष्कर्षों पर चर्चा करें</string>
    <string name="research_outline_key_insights_citations" translate_by="google">- यदि आवश्यक हो तो उद्धरणों का उपयोग करें</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. निष्कर्ष</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- निष्कर्षों का सारांश</string>
    <string name="research_outline_conclusion_implications" translate_by="gemini">निहितार्थ या भविष्य का कार्य</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ निबंध विषय: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 निबंध प्रकार: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 शब्द गणना: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gemini">🗣️ भाषा और लहजा: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="google">🧾 सुझाई गई रूपरेखा:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. परिचय</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">विषय का परिचय: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- पृष्ठभूमि/संदर्भ प्रदान करें</string>
    <string name="essay_outline_introduction_thesis" translate_by="gemini">थीसिस बताएं</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. शरीर अनुच्छेद</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- पैराग्राफ 1: पहला तर्क या बिंदु</string>
    <string name="essay_outline_body_paragraph2" translate_by="google">- पैराग्राफ 2: समर्थन साक्ष्य या कथा</string>
    <string name="essay_outline_body_paragraph3" translate_by="google">- पैराग्राफ 3: काउंटर-तर्क या अतिरिक्त विवरण</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. निष्कर्ष</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- मुख्य बिंदुओं का सारांश दें</string>
    <string name="essay_outline_conclusion_restate" translate_by="gemini">थीसिस को नए तरीके से दोहराएं</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">एक मजबूत अंतिम विचार के साथ समाप्त करें</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ नोट्स:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">%1$s लहजा बनाए रखें</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">लगभग %1$s कुल का लक्ष्य रखें</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">सामान्य %1$s निबंध संरचना का पालन करें</string>
    <string name="literature_outline_title_label" translate_by="gpt">शीर्षक: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">लेखक: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">ध्यान: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">लंबाई: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">शैक्षणिक स्तर: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">रूपरेखा:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. परिचय</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">साहित्यिक कृति और उसके संदर्भ का परिचय दें।</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">लेखक का उल्लेख करें और चुने गए विश्लेषण के फोकस के प्रति प्रासंगिकता बताएं।</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. पृष्ठभूमि</string>
    <string name="literature_outline_background_summary" translate_by="gemini">कथानक या मुख्य पात्रों का सारांश (विश्लेषण के प्रकार पर निर्भर करता है)।</string>
    <string name="literature_outline_background_context" translate_by="gpt">गहन विश्लेषण के लिए आवश्यक संदर्भ प्रदान करें।</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. मुख्य विश्लेषण</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">गहराई से जानें: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">पाठ से सबूत का उपयोग करें: उद्धरण, घटनाएँ, प्रतीकवाद, आदि।</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. कनेक्शन</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">लिंक विश्लेषण को बड़े विषयों या वास्तविक दुनिया के निहितार्थों से जोड़ना।</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">वैकल्पिक रूप से अन्य पात्रों या कार्यों के साथ तुलना करें।</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. निष्कर्ष</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">मुख्य अंतर्दृष्टियों को पुनः व्यक्त करें।</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">शैक्षणिक दृष्टिकोण से काम के मूल्य पर विचार करें।</string>
</resources>
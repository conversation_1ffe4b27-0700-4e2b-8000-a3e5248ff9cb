<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt"><PERSON><PERSON><PERSON></string>
    <string name="placeholder_default" translate_by="gpt">Ex: Insira seu texto aqui…</string>
    <string name="essay_screen_description" translate_by="gemini">Claro! Posso ajudar com isso. Por favor, forneça alguns detalhes para sua redação abaixo.</string>
    <string name="label_choose_topic" translate_by="gpt">Escolha um tópico</string>
    <string name="label_essay_type" translate_by="gpt">Tipo de ensaio</string>
    <string name="label_word_count" translate_by="gpt">Contagem de palavras</string>
    <string name="label_language_tone" translate_by="gpt">Idioma + tom</string>
    <string name="placeholder_topic" translate_by="gpt">Descreva um lugar que te faz sentir em paz.</string>
    <string name="placeholder_essay_type" translate_by="gpt">Ex: Argumentativo, Narrativo…</string>
    <string name="placeholder_word_count" translate_by="gpt">Ex: 300 palavras, 500 palavras, 1000 palavras…</string>
    <string name="placeholder_language_tone" translate_by="gpt">Ex: Formal, acadêmico, …</string>
    <string name="research_screen_description" translate_by="gpt">Um lugar para transformar dados brutos em histórias visuais significativas por meio de pesquisa e análise.</string>
    <string name="label_research_topic" translate_by="gemini">Tópico de pesquisa</string>
    <string name="label_research_goal" translate_by="gpt">Objetivo da pesquisa</string>
    <string name="label_preferred_sources" translate_by="gpt">Fontes preferidas</string>
    <string name="label_depth_length" translate_by="gpt">Profundidade / Comprimento</string>
    <string name="label_academic_level" translate_by="gpt">Nível acadêmico</string>
    <string name="placeholder_research_topic" translate_by="gemini">Mudanças climáticas, O impacto da IA nos empregos, …</string>
    <string name="placeholder_research_goal" translate_by="gpt">Ex: Coleta de Informações, Análise de Tendências…</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Ex: Revistas científicas, livros, artigos oficiais</string>
    <string name="placeholder_depth_length" translate_by="gpt">Ex: 300 palavras, 500 palavras, 1000 palavras…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Ex: Estudantes do ensino médio, estudantes universitários, pesquisa avançada, …</string>
    <string name="literature_screen_description" translate_by="gemini">De palavras a significados ocultos, ajudamos você a descobrir o verdadeiro valor de cada obra literária.</string>
    <string name="label_title_of_work" translate_by="gpt">Título da obra</string>
    <string name="label_author" translate_by="gpt">Autor</string>
    <string name="label_analysis_type" translate_by="gpt">O que você quer analisar?</string>
    <string name="label_format" translate_by="gpt">Comprimento / formato</string>
    <string name="placeholder_title" translate_by="gpt">Ex: O Grande Gatsby</string>
    <string name="placeholder_author" translate_by="gpt">Ex: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Análise de personagens, Temas principais…</string>
    <string name="placeholder_format" translate_by="gpt">Ex: 300 palavras, 500 palavras, 1000 palavras…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Ex: Ensino fundamental, ensino médio ou universidade, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Tópico de Pesquisa: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Objetivo da Pesquisa: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Fontes Preferidas: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Profundidade/Comprimento: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Nível Acadêmico: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Esboço Sugerido:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introdução</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Breve visão geral de %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gpt">- Importância da pesquisa no nível %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Objetivos</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Esclareça o objetivo principal: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodologia</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Abordagem de pesquisa</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Fontes de dados: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Principais Insights</string>
    <string name="research_outline_key_insights_trends" translate_by="google">- Discuta tendências, fatos ou resultados de análise</string>
    <string name="research_outline_key_insights_citations" translate_by="google">- Use citações, se necessário</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusão</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Resumo das descobertas</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implicações ou trabalho futuro</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Tópico da Redação: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Tipo de Redação: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Contagem de Palavras: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Idioma e Tom: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Esboço Sugerido:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introdução</string>
    <string name="essay_outline_introduction_topic" translate_by="gpt">- Introduza o tópico: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Fornecer contexto/fundo</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- Declare a tese</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Parágrafos do Corpo</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Parágrafo 1: Primeiro argumento ou ponto</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Parágrafo 2: Evidências ou narrativa de apoio</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Parágrafo 3: Contra-argumento ou detalhe adicional</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusão</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Resuma os pontos principais</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Reformule a tese de uma nova maneira</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Conclua com uma reflexão final impactante</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notas:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Mantenha um tom de %1$s consistente.</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Aponte para aproximadamente %1$s no total</string>
    <string name="essay_outline_notes_structure" translate_by="gpt">- Siga a estrutura típica do ensaio %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Título: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autor: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Foco: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Comprimento: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Nível Acadêmico: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gemini">Contorno:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introdução</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">Apresente a obra literária e seu contexto.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Mencione o autor e a relevância para o foco de análise escolhido.</string>
    <string name="literature_outline_background_title" translate_by="gemini">2. Plano de Fundo</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Resumo do enredo ou personagens principais (dependendo do tipo de análise).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Forneça o contexto necessário para uma análise mais profunda.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Análise Principal</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Mergulhe fundo em: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Use evidências do texto: citações, eventos, simbolismo, etc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Conexões</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">Análise de links para temas maiores ou implicações no mundo real.</string>
    <string name="literature_outline_connections_contrast" translate_by="gemini">Opcionalmente, compare com outros personagens ou obras.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusão</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Reformule os principais insights.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflita sobre o valor do trabalho a partir de uma perspectiva acadêmica.</string>
</resources>
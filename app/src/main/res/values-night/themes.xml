<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.AndroidAiMath" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/dark_navy</item>
        <item name="colorOnSurface">@color/clr_grey2</item>

        <item name="backgroundColor">@color/dark_navy</item>

        <item name="backgroundLeftChat">#2C81FF</item>
        <item name="backgroundRightChat">#8B9097</item>


        <item name="themeRippleColor">@color/clr_medium_grey</item>
        <item name="themeSecondaryColor">@color/clr_tab_text_inactive</item>
        <item name="themeHintColor">@color/clr_grey</item>
        <item name="themeTextColor">@color/clr_white</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <item name="themeTextSurroundByPrimaryColor">@color/clr_white</item>
    </style>
</resources>
<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="?attr/themeRippleColor">
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <solid android:color="?attr/themeRippleColor" />
            <corners android:radius="@dimen/dimen_radius_corner" />
        </shape>
    </item>

    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <stroke
                android:width="2dp"
                android:color="?attr/colorPrimary" />
            <corners android:radius="@dimen/dimen_radius_corner" />
        </shape>

    </item>
</ripple>
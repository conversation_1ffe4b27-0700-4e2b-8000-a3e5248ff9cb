package amobi.module.common.utils

import amobi.module.common.R
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.PrefComm
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.views.CommActivity
import android.content.IntentSender
import android.util.Log
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.google.android.gms.tasks.Task
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import java.lang.ref.WeakReference
import kotlin.math.abs
import kotlin.math.max

object InAppUpdaterConstant {
    const val FLEXIBLE = AppUpdateType.FLEXIBLE
    const val IMMEDIATE = AppUpdateType.IMMEDIATE
}

class InAppUpdater private constructor(
    activity: CommActivity,
    private val activityResultLauncher: ActivityResultLauncher<IntentSenderRequest>,
) : LifecycleObserver {
    companion object {
        const val TAG = "InAppUpdateManager"

        fun Builder(
            activity: CommActivity,
            activityResultLauncher: ActivityResultLauncher<IntentSenderRequest>,
        ): InAppUpdater {
            return  InAppUpdater(activity, activityResultLauncher)
        }
    }

    fun initialize() {
        val currentVersionCode = MixedUtils.currentVersionCode()
        this.addUpdateInfoListener(
            object : UpdateInfoListener {
                override fun onReceiveVersionCode(
                    code: Int,
                    stalenessDays: Int,
                ) {
                    if (RconfAssist.getInt(RconfComm.COMM_IN_APP_UPDATE_MINIMUM_APP_VERSION) > currentVersionCode) {
                        setMode(InAppUpdaterConstant.IMMEDIATE)
                        start()
                        return
                    }
                    val currentDayOfYear = MixedUtils.currentDayOfYear()
                    if (abs(PrefAssist.getInt(PrefComm.LAST_DAYS_SHOW_UPDATE_PROMPT) - currentDayOfYear) <=
                        max(RconfAssist.getInt(RconfComm.COMM_IN_APP_UPDATE_NEW_VERSION_STALENESS_DAYS_RE_PROMPT), 1)
                    )
                        return
                    if (stalenessDays <= RconfAssist.getInt(RconfComm.COMM_IN_APP_UPDATE_NEW_VERSION_STALENESS_DAYS_THRESHOLD))
                        return
                    PrefAssist.setInt(PrefComm.LAST_DAYS_SHOW_UPDATE_PROMPT, currentDayOfYear)
                    setMode(InAppUpdaterConstant.FLEXIBLE)
                    start()
                }
            },
        )
    }

    private val mActivityWeakReference: WeakReference<CommActivity> = WeakReference(activity)

    // Default mode is FLEXIBLE
    private var mode: Int = InAppUpdaterConstant.FLEXIBLE

    fun getMode(): Int = mode

    // Creates instance of the manager.
    private val appUpdateManager = AppUpdateManagerFactory.create(activity)

    // Returns an intent object that you use to check for an update.
    private val appUpdateInfoTask: Task<AppUpdateInfo> = appUpdateManager.appUpdateInfo
    private var flexibleUpdateDownloadListener: FlexibleUpdateDownloadListener? = null

    private fun setMode(mode: Int): InAppUpdater {
        Log.d(TAG, "Set update mode to : ${if (mode == InAppUpdaterConstant.FLEXIBLE) "FLEXIBLE" else "IMMEDIATE"}")
        this.mode = mode
        return this
    }

    fun start() {
        if (mode == InAppUpdaterConstant.FLEXIBLE) {
            setUpListener()
        }
        checkUpdate()
    }

    fun restartImmediateUpdate() {
        try {
            Log.d(TAG, "Starting update")
            appUpdateManager
                ?.appUpdateInfo
                ?.addOnSuccessListener { appUpdateInfo ->
                    // If the update is downloaded but not installed,
                    val activity = mActivityWeakReference.get() ?: return@addOnSuccessListener
                    if (activity.isNotSafe()) return@addOnSuccessListener
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activityResultLauncher,
                        AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build(),
                    )
                }
        } catch (e: IntentSender.SendIntentException) {
            Log.d(TAG, "" + e.message)
        }
    }

    private fun checkUpdate() {
        // Checks that the platform will allow the specified type of update.
        Log.d(TAG, "Checking for updates")
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            val activity = mActivityWeakReference.get() ?: return@addOnSuccessListener
            if (activity.isNotSafe()) return@addOnSuccessListener

            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
                appUpdateInfo.isUpdateTypeAllowed(mode)
            ) {
                // Request the update.
                Log.d(TAG, "Update available")
                startUpdate(appUpdateInfo)
            } else {
                Log.d(TAG, "No Update available")
            }
        }
    }

    private fun startUpdate(appUpdateInfo: AppUpdateInfo) {
        val activity = mActivityWeakReference.get() ?: return
        if (activity.isNotSafe()) return
        try {
            Log.d(TAG, "Starting update")
            appUpdateManager?.startUpdateFlowForResult(
                appUpdateInfo,
                activityResultLauncher,
                AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build(),
            )
        } catch (e: IntentSender.SendIntentException) {
            Log.d(TAG, "" + e.message)
        }
    }

    //    public static void handleResult(int requestCode, int resultCode){
    //        Log.d("LIBRARY_ZMA", "Req code Update : " + requestCode);
    //        if (requestCode == InAppUpdaterConstant.REQUEST_CODE) {
    //            Log.d("LIBRARY_ZMA", "Result code Update : " + resultCode);
    //            if (resultCode != RESULT_OK) {
    //                Log.d("LIBRARY_ZMA", "Update flow failed! Result code: " + resultCode);
    //                // If the update is cancelled or fails,
    //                // you can request to start the update again.
    //            }
    //        }
    //    }
    private val listener: InstallStateUpdatedListener =
        InstallStateUpdatedListener { installState ->
            if (installState.installStatus() == InstallStatus.DOWNLOADING) {
                val bytesDownloaded = installState.bytesDownloaded()
                val totalBytesToDownload = installState.totalBytesToDownload()
                if (flexibleUpdateDownloadListener != null) {
                    flexibleUpdateDownloadListener!!.onDownloadProgress(bytesDownloaded, totalBytesToDownload)
                }
            }
            if (installState.installStatus() == InstallStatus.DOWNLOADED) {
                // After the update is downloaded, show a notification
                // and request user confirmation to restart the app.
                Log.d(TAG, "An update has been downloaded")
                popupSnackbarForCompleteUpdate()
            }
        }

    init {
        activity.lifecycle.addObserver(this)
    }

    private fun setUpListener() {
        appUpdateManager?.registerListener(listener)
    }

    private fun continueUpdate() {
        if (mode == InAppUpdaterConstant.FLEXIBLE) {
            continueUpdateForFlexible()
        } else {
            continueUpdateForImmediate()
        }
    }

    private fun continueUpdateForFlexible() {
        val appUpdateManager = appUpdateManager ?: return
        appUpdateManager
            .appUpdateInfo
            .addOnSuccessListener { appUpdateInfo ->
                // If the update is downloaded but not installed,
                val activity = mActivityWeakReference.get() ?: return@addOnSuccessListener
                if (activity.isNotSafe()) return@addOnSuccessListener
                // notify the user to complete the update.
                if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                    Log.d(TAG, "An update has been downloaded")
                    popupSnackbarForCompleteUpdate()
                }
            }
    }

    private fun continueUpdateForImmediate() {
        val appUpdateManager = appUpdateManager ?: return
        appUpdateManager
            .appUpdateInfo
            .addOnSuccessListener { appUpdateInfo ->
                if (appUpdateInfo.updateAvailability()
                    == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS
                ) {
                    // If an in-app update is already running, resume the update.
                    val activity = mActivityWeakReference.get() ?: return@addOnSuccessListener
                    if (activity.isNotSafe()) return@addOnSuccessListener

                    try {
                        appUpdateManager.startUpdateFlowForResult(
                            appUpdateInfo,
                            activityResultLauncher,
                            AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build(),
                        )
                    } catch (e: IntentSender.SendIntentException) {
                        Log.d(TAG, "" + e.message)
                    }
                }
            }
    }

    private fun popupSnackbarForCompleteUpdate() {
        // https://docs.google.com/spreadsheets/d/1u00nj0owW0jnBMr1C7BOnUGHr4zYJYS22fxT2uTJw6c/edit
        val appUpdateManager = appUpdateManager ?: return
        val activity = mActivityWeakReference.get() ?: return
        if (activity.isNotSafe()) return
        val viewContent = activity.window.decorView.findViewById<View?>(R.id.content) ?: return
        val snackbar =
            Snackbar.make(
                viewContent,
                R.string.in_app_update_downloaded_noti,
                Snackbar.LENGTH_INDEFINITE,
            )
        snackbar.setAction(
            activity.getString(R.string.in_app_update_restart).uppercase(),
        ) { appUpdateManager.completeUpdate() }
        snackbar.show()
    }

    fun addUpdateInfoListener(updateInfoListener: UpdateInfoListener) {
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                // Request the update.
                Log.d(TAG, "Update available")
                val availableVersionCode = appUpdateInfo.availableVersionCode()
                val stalenessDays = appUpdateInfo.clientVersionStalenessDays() ?: -1

                // You can get the available version code of the apk in Google Play
                // Do something here
                // Number of days passed since the user was notified of an update through the Google Play
                // If the user hasn't notified this will return -1 as days
                // You can decide the type of update you want to call
                updateInfoListener.onReceiveVersionCode(availableVersionCode, stalenessDays)
            } else {
                Log.d(TAG, "No Update available")
            }
        }
        if (CommFigs.IS_SHOW_TEST_OPTION) {
            appUpdateInfoTask.addOnFailureListener { e ->
                Log.d(TAG, "Failed to get update info: $e")
            }
        }
    }

    fun addFlexibleUpdateDownloadListener(flexibleUpdateDownloadListener: FlexibleUpdateDownloadListener?) {
        this.flexibleUpdateDownloadListener = flexibleUpdateDownloadListener
    }

    private fun unregisterListener() {
        if (appUpdateManager != null) {
            appUpdateManager.unregisterListener(listener)
            Log.d(TAG, "Unregistered the install state listener")
        }
    }

    interface UpdateInfoListener {
        fun onReceiveVersionCode(
            code: Int,
            stalenessDays: Int,
        )
    }

    interface FlexibleUpdateDownloadListener {
        fun onDownloadProgress(
            bytesDownloaded: Long,
            totalBytes: Long,
        )
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    private fun onResume() {
        continueUpdate()
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    private fun onDestroy() {
        unregisterListener()
    }
}

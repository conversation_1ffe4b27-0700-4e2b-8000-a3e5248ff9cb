package amobi.module.common.views

import amobi.module.common.R
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.utils.ConnectionState
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.InAppUpdater
import amobi.module.common.utils.InAppUpdaterConstant
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.checkConnection
import amobi.module.common.utils.dlog
import android.app.Activity
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.window.core.layout.WindowHeightSizeClass
import androidx.window.core.layout.WindowSizeClass
import androidx.window.core.layout.WindowWidthSizeClass
import androidx.window.layout.WindowMetricsCalculator

abstract class CommActivity : AppCompatActivity() {
    private var updateManager: InAppUpdater? = null

    fun isInAppManagerImmediateUpdate(): Boolean = updateManager?.getMode() == InAppUpdaterConstant.IMMEDIATE

    val updateManagerResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            when (result.resultCode) {
                Activity.RESULT_OK ->
                    dlog(InAppUpdater.TAG + " Update flow completed!")

                else ->
                    if (isInAppManagerImmediateUpdate()) {
                        MixedUtils.showToast(this, this.getString(R.string.in_app_please_update_to_continue_using_app))
                        updateManager?.restartImmediateUpdate()
                    }
            }
        }

    protected fun hideNavigationBarUI() {
        WindowInsetsControllerCompat(window, window.decorView).let { controller ->
            controller.hide(WindowInsetsCompat.Type.navigationBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }

    protected fun showNavigationBarUI() {
        WindowInsetsControllerCompat(window, window.decorView).show(WindowInsetsCompat.Type.navigationBars())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)

        // ===================== IN-APP UPDATE =====================
        if (RconfAssist.getBoolean(RconfComm.COMM_IN_APP_UPDATE_ENABLED)) {
            updateManager = InAppUpdater.Builder(this, updateManagerResultLauncher)
            updateManager?.initialize()
        }
    }

    protected open fun onConnectionStateChanged(state: ConnectionState) {
    }

    protected fun initConnectivityListener() {
        CommFigs.WEB_CONNECTION_STATE = ConnectionState.CONNECTED
        checkConnection(this) { state ->
            CommFigs.WEB_CONNECTION_STATE = state
            onConnectionStateChanged(state)
            if (!CommFigs.IS_DEBUG) return@checkConnection
            when (state) {
                ConnectionState.CONNECTED -> {
                    DebugLogCustom.logd("" + "INTERNET CONNECTED")
//                    MixedUtils.showToastImmediately(this, "INTERNET CONNECTED")
                }

                ConnectionState.SLOW -> {
                    DebugLogCustom.logd("" + "INTERNET SLOW")
//                    MixedUtils.showToastImmediately(this, "INTERNET SLOW")
                }

                else -> {
                    DebugLogCustom.logd("" + "INTERNET DISCONNECTED")
//                    MixedUtils.showToastImmediately(this, "INTERNET DISCONNECTED")
                }
            }
        }
    }

    open fun isNotSafe(): Boolean = this.isFinishing || this.isDestroyed

    open fun isSafe(): Boolean = !isNotSafe()

    /** Determines whether the device has a compact screen. **/
    open fun isScreenCompact(): Boolean {
        val metrics = WindowMetricsCalculator.getOrCreate().computeMaximumWindowMetrics(this)
        val width = metrics.bounds.width()
        val height = metrics.bounds.height()
        val density = resources.displayMetrics.density
        val windowSizeClass = WindowSizeClass.compute(width / density, height / density)

        return windowSizeClass.windowWidthSizeClass == WindowWidthSizeClass.COMPACT ||
            windowSizeClass.windowHeightSizeClass == WindowHeightSizeClass.COMPACT
    }
}

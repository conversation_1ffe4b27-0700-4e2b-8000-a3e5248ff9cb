package amobi.module.common.configs

import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig

object RconfAssist {
    private const val TEST_TAG = "RconfAssist_"

    fun getBoolean(key: String): Boolean =
        if (CommFigs.IS_SHOW_TEST_OPTION)
            PrefAssist.getBoolean(TEST_TAG + key, Firebase.remoteConfig.getBoolean(key))
        else
            Firebase.remoteConfig.getBoolean(key)

    fun setTestBoolean(
        key: String,
        value: Boolean,
    ) {
        if (!CommFigs.IS_SHOW_TEST_OPTION) return
        PrefAssist.setBoolean(TEST_TAG + key, value)
    }

    fun getLong(key: String): Long =
        if (CommFigs.IS_SHOW_TEST_OPTION)
            PrefAssist.getLong(TEST_TAG + key, Firebase.remoteConfig.getLong(key))
        else
            Firebase.remoteConfig.getLong(key)

    fun setTestLong(
        key: String,
        value: Long,
    ) {
        if (!CommFigs.IS_SHOW_TEST_OPTION) return
        PrefAssist.setLong(TEST_TAG + key, value)
    }

    fun getInt(key: String): Int =
        if (CommFigs.IS_SHOW_TEST_OPTION)
            PrefAssist.getInt(TEST_TAG + key, Firebase.remoteConfig.getLong(key).toInt())
        else
            Firebase.remoteConfig.getLong(key).toInt()

    fun setTestInt(
        key: String,
        value: Int,
    ) {
        if (!CommFigs.IS_SHOW_TEST_OPTION) return
        PrefAssist.setInt(TEST_TAG + key, value)
    }

    fun getString(key: String): String =
        if (CommFigs.IS_SHOW_TEST_OPTION)
            PrefAssist.getString(TEST_TAG + key, Firebase.remoteConfig.getString(key))
        else
            Firebase.remoteConfig.getString(key)

    fun setTestString(
        key: String,
        value: String,
    ) {
        if (!CommFigs.IS_SHOW_TEST_OPTION) return
        PrefAssist.setString(TEST_TAG + key, value)
    }
}

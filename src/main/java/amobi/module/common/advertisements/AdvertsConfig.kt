package amobi.module.common.advertisements

import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.PrefComm
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.Locale

enum class AdvertsRequestStatus {
    NOT_REQUEST,
    REQUESTING,
    RESPONSE_OK,
    RESPONSE_ERROR,
    PURCHASED,
}

class AdvertsConfig private constructor() {
    var isHideAd = false
    var isTestAdsMode = false

    var isEnableBannerAds: Boolean = true
    var isEnableNativeAds: Boolean = true
    var isEnableInterOpenAds: Boolean = true
    var isEnableInterInsideAds: Boolean = true
    var isEnableOpenAdsOpen: Boolean = true
    var isEnableOpenAdsResume: Boolean = true

    init {
        if (CommFigs.IS_ADD_TEST_DEVICE) {
            isTestAdsMode = PrefAssist.getBoolean(PrefComm.ALPHA_TEST_AD, CommFigs.IS_ALPHA)
        }
    }

    fun updateEnableAdsBooleans() {
        isEnableBannerAds = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_BANNER_ADS)
        isEnableNativeAds = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_NATIVE_ADS)
        isEnableInterOpenAds = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_INTER_OPEN_ADS)
        isEnableInterInsideAds = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_INTER_ACTION_ADS)
        isEnableOpenAdsOpen = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_OPEN_ADS_OPEN)
        isEnableOpenAdsResume = RconfAssist.getBoolean(RconfComm.COMM_ENABLE_OPEN_ADS_RESUME)
    }

    @SuppressLint("HardwareIds")
    fun getAdMobDeviceAndroidId(context: Context): String {
        try {
            val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            // Create MD5 Hash
            val digest = MessageDigest.getInstance("MD5")
            digest.update(androidId.toByteArray())
            val messageDigest = digest.digest()
            // Create Hex String
            val hexString = StringBuffer()
            for (i in messageDigest.indices) {
                var h = Integer.toHexString(0xFF and messageDigest[i].toInt())
                while (h.length < 2) h = "0$h"
                hexString.append(h)
            }
            return hexString.toString().uppercase(Locale.getDefault())
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return ""
    }

    companion object {
        const val TEST_BANNER_AD_ID = "ca-app-pub-3940256099942544/6300978111"
        const val TEST_NATIVE_AD_ID = "ca-app-pub-3940256099942544/2247696110"
        const val TEST_INTER_AD_ID = "ca-app-pub-3940256099942544/1033173712"
        const val TEST_OPEN_AD_ID = "ca-app-pub-3940256099942544/9257395921"
        const val TEST_REWARD_AD_ID = "ca-app-pub-3940256099942544/1712485313"
        const val TEST_REWARD_INTER_AD_ID = "ca-app-pub-3940256099942544/6978759866"

        private var shared: AdvertsConfig? = null
        val instance: AdvertsConfig
            get() {
                if (shared == null) {
                    shared = AdvertsConfig()
                }
                return shared!!
            }
    }
}

package amobi.module.common.advertisements.open_ad

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.views.CommActivity

object AdvertsManagerOpenAd {
    private var appOpenAdOnOpenManager: AdvertsInstanceOpenAd? = null
    private var appOpenAdOnResumeManager: AdvertsInstanceOpenAd? = null

    fun requestOpenAdAdverts(
        adId: String,
        isOpenApp: Boolean,
    ) {
        if (AdvertsConfig.instance.isHideAd) return

        if (isOpenApp) {
            if (!AdvertsConfig.instance.isEnableOpenAdsOpen) return
            if (appOpenAdOnOpenManager?.isAdvertsAvailable == true || appOpenAdOnOpenManager?.isAdvertsLoading == true) return
            if (appOpenAdOnOpenManager == null || !appOpenAdOnOpenManager!!.isAdvertsAvailable) {
                appOpenAdOnOpenManager = AdvertsInstanceOpenAd(adId, true, adTag = "Open_App_Open")
            }
            appOpenAdOnOpenManager!!.requestOpenAd(CommApplication.appContext)
        } else {
            if (!AdvertsConfig.instance.isEnableOpenAdsResume) return
            if (appOpenAdOnResumeManager?.isAdvertsAvailable == true || appOpenAdOnResumeManager?.isAdvertsLoading == true) return
            if (appOpenAdOnResumeManager == null || !appOpenAdOnResumeManager!!.isAdvertsAvailable) {
                appOpenAdOnResumeManager = AdvertsInstanceOpenAd(adId, false)
            }
            appOpenAdOnResumeManager!!.requestOpenAd(CommApplication.appContext)
        }
    }

    fun isOpenAdOpenAppAvailable(): Boolean = appOpenAdOnOpenManager?.isAdvertsAvailable == true

    fun isOpenAdOpenAppAvailableOrLoading(): Boolean = appOpenAdOnOpenManager?.isAdvertsAvailable == true || appOpenAdOnOpenManager?.isAdvertsLoading == true

    fun showOpenAdOpenAppIfAvailable(
        activity: CommActivity,
        isBypassTimeDelay: Boolean = false,
        onShowAdCompleteListener: (() -> Unit?)? = null,
    ): Boolean {
        if (appOpenAdOnOpenManager == null || activity.isNotSafe()) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        return appOpenAdOnOpenManager?.showAdIfAvailable(
            activity = activity,
            isBypassTimeDelay = isBypassTimeDelay,
            placement = "AO_OPEN",
            onShowAdCompleteListener = onShowAdCompleteListener,
        ) == true
    }

    fun showOpenAdResumeIfAvailable(
        activity: CommActivity,
        isBypassTimeDelay: Boolean = false,
        onShowAdCompleteListener: (() -> Unit?)? = null,
    ): Boolean {
        if (appOpenAdOnResumeManager == null || activity.isNotSafe()) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        return appOpenAdOnResumeManager?.showAdIfAvailable(
            activity = activity,
            isBypassTimeDelay = isBypassTimeDelay,
            placement = "AO_RESUME",
            onShowAdCompleteListener = onShowAdCompleteListener,
        ) == true
    }
}

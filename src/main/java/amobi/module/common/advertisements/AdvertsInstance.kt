package amobi.module.common.advertisements

import amobi.module.common.advertisements.interstitial_ad.AdvertsInstanceInter
import amobi.module.common.advertisements.open_ad.AdvertsInstanceOpenAd
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.FirebaseAssist
import android.os.Bundle
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdValue

open class AdvertsInstance(
    private val adTag: String = "",
) {
    companion object {
        const val ACTION_AD_CLICKED = "action_ad_clicked"
        const val ACTION_AD_CLICKED_AD_ID = "action_ad_clicked_ad_id"

        private const val kLASTIME_SHOW_FULL_ACTION_ADS = "kLASTIME_SHOW_FULL_ACTION_ADS"
        private const val kVALUE_MICROS_COUNTED = "kVALUE_MICROS_COUNTED"
        private const val kVALUE_MICROS_RECORDED = "kVALUE_MICROS_RECORDED"

        fun clearLastTimeShowedFullAd() {
            PrefAssist.setLong(kLASTIME_SHOW_FULL_ACTION_ADS, 0L)
        }

        fun setLastTimeShowedFullAd(time: Long) {
            PrefAssist.setLong(kLASTIME_SHOW_FULL_ACTION_ADS, time)
        }

        fun getLastTimeShowedFullAd(): Long {
            val lastTime = PrefAssist.getLong(kLASTIME_SHOW_FULL_ACTION_ADS, 0L)
            if (lastTime > System.currentTimeMillis()) {
                PrefAssist.setLong(kLASTIME_SHOW_FULL_ACTION_ADS, 0L)
                return 0L
            }
            return lastTime
        }
    }

    var beginLoadAdSeconds = 0L
    var lastRequestLoadAdMillis = 0L
    var admobErrorCode: Int? = null
    protected var requestSuccessMillis = 0L

    internal var adUnitId: String? = null
    var requestStatus = AdvertsRequestStatus.NOT_REQUEST

    val isAdvertsLoading: Boolean
        get() =
            this.requestStatus == AdvertsRequestStatus.REQUESTING &&
                System.currentTimeMillis() - lastRequestLoadAdMillis < CommFigs.MILLIS_MINUTE
    val isAdmobNoFill: Boolean
        get() =
            admobErrorCode == AdRequest.ERROR_CODE_NO_FILL &&
                System.currentTimeMillis() - lastRequestLoadAdMillis < 5 * CommFigs.MILLIS_MINUTE

    internal var listAdsID = arrayOf<String>()

    internal fun findNextAdsId(adId: String?): String? {
        if (listAdsID.size <= 1) return null
        val index = listAdsID.indexOf(adId)
        if (index == -1) return null
        return listAdsID.getOrNull(index + 1)
    }

    fun sendLogAdverts(
        adValue: AdValue,
        mediationAdapterClassName: String?,
    ) {
        if (adUnitId == null) return
        val params = Bundle()
        params.putLong("valuemicros", adValue.valueMicros)
        // These values below won’t be used in ROAS recipe.
        // But log for purposes of debugging and future reference.
        params.putString("currency", adValue.currencyCode)
        params.putInt("precision", adValue.precisionType)
        params.putString("adunitid", adUnitId)
        params.putString("network", mediationAdapterClassName)
        FirebaseAssist.instance.logPaidAdvertsClicked(params)

        sendLogX5Adverts(adValue, mediationAdapterClassName)
    }


    private fun sendLogX5Adverts(adValue: AdValue, mediationAdapterClassName: String?) {
        if (adUnitId == null) return

        val valueCounted = PrefAssist.getInt(kVALUE_MICROS_COUNTED)
        val valueRecorded = PrefAssist.getLong(kVALUE_MICROS_RECORDED)
        if (valueCounted <= 4) {
            PrefAssist.setInt(kVALUE_MICROS_COUNTED, valueCounted + 1)
            PrefAssist.setLong(kVALUE_MICROS_RECORDED, valueRecorded + adValue.valueMicros)
            return
        }
        val params = Bundle()
        params.putLong("valuemicros", valueRecorded + adValue.valueMicros)
        // These values below won’t be used in ROAS recipe.
        // But log for purposes of debugging and future reference.
        params.putString("currency", adValue.currencyCode)
        params.putInt("precision", adValue.precisionType)
        params.putString("adunitid", adUnitId)
        params.putString("network", mediationAdapterClassName)
        FirebaseAssist.instance.logPaidAdvertsX5Clicked(params)

        PrefAssist.setInt(kVALUE_MICROS_COUNTED, 0)
        PrefAssist.setLong(kVALUE_MICROS_RECORDED, 0)
    }

    fun sendLogAdDuration(
        adMetrics: AdDurationTracker.AdMetrics,
        adValueMicros: Long,
        mediationAdapterClassName: String?,
        adSourceName: String?,
        placement: String?,
    ) {
        if (adUnitId == null) return

        val adDuration = adMetrics.adDuration
        val adClick = adMetrics.adClickTime
        val adBounceBack = adMetrics.adBounceBackTime
        val adClickCount = adMetrics.adClickCount

        val parameters = Bundle()
        parameters.putString("ad_source", adSourceName)
        parameters.putLong("ad_value", adValueMicros)
        when (this) {
            is AdvertsInstanceInter -> parameters.putString("format", "Interstitial")
            is AdvertsInstanceOpenAd -> parameters.putString("format", "OpenApp")
            else -> parameters.putString("format", "Unknown")
        }
        parameters.putInt("ad_duration", adDuration)
        parameters.putInt("ad_click", adClick ?: 0)
        parameters.putInt("ad_bounce_back", adBounceBack ?: 0)
        parameters.putInt("ad_click_count", adClickCount)
        parameters.putString("platform", mediationAdapterClassName)
        if (!placement.isNullOrEmpty()) parameters.putString("placement", placement)

        // Log the event with your analytics framework
        FirebaseAssist.instance.logAdvertsDuration(parameters)
    }

    fun getAdsName(): String {
        if (adTag.isNotEmpty()) return "$adTag(${adUnitId!!.split("/")[1]})"
        return adUnitId!!.split("/")[1]
    }
}

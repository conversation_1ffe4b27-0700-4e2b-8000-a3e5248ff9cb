package amobi.module.common.advertisements.interstitial_ad

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdDurationTracker
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.views.CommActivity
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdValue
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback

class AdvertsInstanceInter(
    listAdsID: Array<String>,
    isOpenApp: Boolean,
    private val adTag: String = "",
) : AdvertsInstance(adTag) {
    companion object {
        private const val EXPIRED_TIME = (1 * CommFigs.MILLIS_HOUR) - (CommFigs.MILLIS_MINUTE)
    }

    private val isOpenApp: Boolean
    private var mFullAds: InterstitialAd? = null
    private var isShowingAd = false

    init {
        this.listAdsID =
            if (AdvertsConfig.instance.isTestAdsMode) {
                arrayOf(AdvertsConfig.TEST_INTER_AD_ID)
            } else {
                listAdsID
            }
        adUnitId = this.listAdsID[0]
        this.isOpenApp = isOpenApp
    }

    val isAdvertsAvailable: Boolean
        get() = mFullAds != null && isNotExpired

    private val isNotExpired: Boolean
        get() = System.currentTimeMillis() - requestSuccessMillis < EXPIRED_TIME

    fun requestFullAds(onAdLoadedListener: ((Boolean) -> Unit?)? = null) {
        val advertsConfig = AdvertsConfig.instance
        val isEnableInterstitial = if (isOpenApp) true else advertsConfig.isEnableInterInsideAds
        if (advertsConfig.isHideAd || !isEnableInterstitial) {
            onAdLoadedListener?.invoke(false)
            return
        }
        if (isAdvertsAvailable || isAdvertsLoading) {
            onAdLoadedListener?.invoke(false)
            return
        }

        requestStatus = AdvertsRequestStatus.REQUESTING
        lastRequestLoadAdMillis = System.currentTimeMillis()

        if (mFullAds == null) {
            beginLoadAdSeconds = MixedUtils.currentTimeSeconds()
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
        } else {
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
        }
        val adRequest = AdRequest.Builder().build()

        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🟡: REQUEST BEGIN", CommFigs.LOG_TAG_INTER_AD)

        InterstitialAd.load(
            CommApplication.appContext,
            adUnitId!!,
            adRequest,
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(interstitialAd: InterstitialAd) {
                    super.onAdLoaded(interstitialAd)
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🟢: REQUEST SUCCESS", CommFigs.LOG_TAG_INTER_AD)

                    requestSuccessMillis = System.currentTimeMillis()
                    // The mInterstitialAd reference will be null until
                    // an ad is loaded.
                    mFullAds = interstitialAd
                    interstitialAd.onPaidEventListener =
                        OnPaidEventListener { adValue: AdValue ->
                            DebugLogCustom.logd(" ${getAdsName()} 🟢: onPaidEvent: $adValue", CommFigs.LOG_TAG_INTER_AD)
                            val mediationAdapter = interstitialAd.responseInfo.mediationAdapterClassName
                            sendLogAdverts(adValue, mediationAdapter)

                            adValueMicros = adValue.valueMicros
                            mediationAdapterClassName = mediationAdapter ?: ""
                            adSourceName = interstitialAd.responseInfo.loadedAdapterResponseInfo?.adSourceName ?: ""
                            checkSendLogDuration()
                        }
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)

                    admobErrorCode = null
                    requestStatus = AdvertsRequestStatus.RESPONSE_OK
                    onAdLoadedListener?.invoke(true)
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    admobErrorCode = loadAdError.code
                    super.onAdFailedToLoad(loadAdError)
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: REQUEST ERROR " + loadAdError.code + " -> " + loadAdError.message, CommFigs.LOG_TAG_INTER_AD)

                    mFullAds = null
                    FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_LOAD_FAILED, beginLoadAdSeconds, loadAdError.code)
                    requestStatus = AdvertsRequestStatus.RESPONSE_ERROR

                    val newAdId = findNextAdsId(adUnitId)
                    if (!newAdId.isNullOrEmpty()) {
                        adUnitId = newAdId
                        requestFullAds(onAdLoadedListener = onAdLoadedListener)
                    }
                }
            },
        )
    }

    fun showFullAds(
        activity: CommActivity,
        isBypassTimeDelay: Boolean = false,
        isFillCache: Boolean = true,
        placement: String? = null,
        onShowAdCompleteListener: (() -> Unit?)? = null,
        onAdDismissedListener: (() -> Unit?)? = null,
    ): Boolean {
        val advertsConfig = AdvertsConfig.instance
        val isEnableInterstitial = if (isOpenApp) advertsConfig.isEnableInterOpenAds else advertsConfig.isEnableInterInsideAds
        if (advertsConfig.isHideAd || !isEnableInterstitial) {
            onShowAdCompleteListener?.invoke()
            return false
        }
        if (isShowingAd) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD ALREADY SHOWING", CommFigs.LOG_TAG_OPEN_AD)
            onShowAdCompleteListener?.invoke()
            return false
        }

        if (!isAdvertsAvailable) {
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🔵: AD WASN'T READY", CommFigs.LOG_TAG_INTER_AD)
            onShowAdCompleteListener?.invoke()
            FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)

            if (!isOpenApp && isFillCache) requestFullAds()

            return false
        }
        val currentTime = System.currentTimeMillis()

        val isBypassTimeDelayCorrected =
            if (isBypassTimeDelay && !RconfAssist.getBoolean(RconfComm.COMM_DELAY_INTER_ADS_BYPASS_ALLOWED)) {
                false
            } else {
                isBypassTimeDelay
            }

        if (!isBypassTimeDelayCorrected) {
            val oldTime = getLastTimeShowedFullAd()
            if (currentTime - oldTime < RconfAssist.getLong(RconfComm.COMM_DELAY_INTER_ADS) * CommFigs.MILLIS_SECOND) {
                onShowAdCompleteListener?.invoke()
                return false
            }
        }
        setLastTimeShowedFullAd(currentTime)
        isShowingAd = true

        resetLogDurationVariables()
        this.placement = placement
        mFullAds?.fullScreenContentCallback =
            object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: AD CLICKED", CommFigs.LOG_TAG_INTER_AD)
                    AdDurationTracker.instance.onUserClickedAd()

                    FirebaseAssist.instance.logClickAd(FirebaseAssist.AD_INTERSTITIAL, beginLoadAdSeconds)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logClickSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, beginLoadAdSeconds)

                    LocalBroadcastManager
                        .getInstance(CommApplication.appContext)
                        .sendBroadcast(
                            Intent().apply {
                                action = AdvertsInstance.ACTION_AD_CLICKED
                                putExtra(AdvertsInstance.ACTION_AD_CLICKED_AD_ID, adUnitId)
                            },
                        )
                }

                override fun onAdDismissedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 😔: DISMISSED FULL AD", CommFigs.LOG_TAG_INTER_AD)
                    setLastTimeShowedFullAd(System.currentTimeMillis())

                    adMetrics = AdDurationTracker.instance.onAdDismissedFullScreenContent().copy()
                    CommApplication.fullScreenAdInstance = null
                    checkSendLogDuration()

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: TRY DISMISSED FULL AD ALREADY FAIL TO SHOW ", CommFigs.LOG_TAG_INTER_AD)
                        return
                    }

                    mFullAds?.fullScreenContentCallback = null
                    mFullAds = null
                    isShowingAd = false

                    onShowAdCompleteListener?.invoke()
                    onAdDismissedListener?.invoke()

                    if (!isOpenApp && isFillCache) requestFullAds()

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🔴: FAILED TO DISPLAY FULL AD: " + adError.code + " -> " + adError.message, CommFigs.LOG_TAG_INTER_AD)

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_FAILED)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_FAILED)

                    CommApplication.fullScreenAdInstance = null

                    if (!isShowingAd) {
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            DebugLogCustom.logd(getAdsName() + " 🔴: CALL FAILED FULL AD ALREADY SHOW ", CommFigs.LOG_TAG_INTER_AD)
                        return
                    }

                    mFullAds?.fullScreenContentCallback = null
                    mFullAds = null
                    isShowingAd = false
                    onShowAdCompleteListener?.invoke()

                    if (!isOpenApp && isFillCache) requestFullAds()

                    clearLastTimeShowedFullAd()

                    CommApplication.isShowingFullscreenAds = false
                }

                override fun onAdImpression() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: IMPRESSION FULL AD", CommFigs.LOG_TAG_INTER_AD)
                }

                override fun onAdShowedFullScreenContent() {
                    if (CommFigs.IS_SHOW_TEST_OPTION)
                        DebugLogCustom.logd(getAdsName() + " 🤩: DISPLAYED FULL AD", CommFigs.LOG_TAG_INTER_AD)

                    AdDurationTracker.instance.onAdShowedFullScreenContent()
                    CommApplication.fullScreenAdInstance = this@AdvertsInstanceInter

                    FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_SUCCESS)
                    if (adTag.isNotEmpty())
                        FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_INTERSTITIAL, FirebaseAssist.AD_SHOW_SUCCESS)

                    CommApplication.isShowingFullscreenAds = true
                }
            }
        mFullAds?.show(activity)

        return true
    }

    // AdMob Track Duration ===============================
    private var adMetrics: AdDurationTracker.AdMetrics? = null
    private var adValueMicros: Long? = null
    private var mediationAdapterClassName: String? = null
    private var adSourceName: String? = null
    private var placement: String? = null

    private fun checkSendLogDuration() {
        val adMetrics = adMetrics ?: return
        val adValueMicros = adValueMicros ?: return
        val mediationAdapterClassName = mediationAdapterClassName ?: return
        val adSourceName = adSourceName ?: return
        sendLogAdDuration(adMetrics, adValueMicros, mediationAdapterClassName, adSourceName, placement)
        resetLogDurationVariables()
    }

    private fun resetLogDurationVariables() {
        adMetrics = null
        adValueMicros = null
        mediationAdapterClassName = null
        adSourceName = null
    }

    fun onActivityPaused() {
        DebugLogCustom.logd(" ${getAdsName()} ON PAUSED", CommFigs.LOG_TAG_INTER_AD)
        AdDurationTracker.instance.onUserLeavesApp()
    }

    fun onActivityResumed() {
        DebugLogCustom.logd(" ${getAdsName()} ON RESUMED", CommFigs.LOG_TAG_INTER_AD)
        AdDurationTracker.instance.onUserReturnsToApp()
    }
}

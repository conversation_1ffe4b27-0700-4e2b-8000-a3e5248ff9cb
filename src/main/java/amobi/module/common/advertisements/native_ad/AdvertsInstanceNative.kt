package amobi.module.common.advertisements.native_ad

import amobi.module.common.CommApplication
import amobi.module.common.R
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.AdvertsRequestStatus
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.ConnectionState
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import android.content.Intent
import android.view.View
import android.view.ViewStub
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.facebook.shimmer.ShimmerFrameLayout
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdValue
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.VideoOptions
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import java.lang.ref.WeakReference

class AdvertsInstanceNative(
    listAdsID: Array<String>,
    private var adChoicesPlacement: Int,
    private val adTag: String = "",
) : AdvertsInstance(adTag) {
    companion object {
        private const val EXPIRED_TIME = (1 * CommFigs.MILLIS_HOUR) - (CommFigs.MILLIS_MINUTE)
    }

    private var mNativeAd: NativeAd? = null
    private var nativeTemplateView: WeakReference<NativeTemplateView> = WeakReference(null)
    private var templateContainerView: WeakReference<View> = WeakReference(null)
    private var placeHolderContainerView: WeakReference<View> = WeakReference(null)

    private var onShowNativeAdCallback: ((Boolean) -> Unit)? = null
    private var onClickNativeAdCallback: (() -> Unit)? = null

    init {
        this.listAdsID =
            if (AdvertsConfig.instance.isTestAdsMode) {
                arrayOf(AdvertsConfig.TEST_NATIVE_AD_ID)
            } else {
                listAdsID
            }
        adUnitId = this.listAdsID[0]
    }

    val isAdvertsAvailable: Boolean
        get() = mNativeAd != null && isNotExpired

    private val isNotExpired: Boolean
        get() = System.currentTimeMillis() - requestSuccessMillis < EXPIRED_TIME

    fun requestNativeAdverts(muteMedia: Boolean = true) {
        val advertsConfig = AdvertsConfig.instance
        if (advertsConfig.isHideAd || !advertsConfig.isEnableNativeAds) return
        if (isAdvertsAvailable || isAdvertsLoading) return

        requestStatus = AdvertsRequestStatus.REQUESTING
        lastRequestLoadAdMillis = System.currentTimeMillis()

        if (mNativeAd == null) {
            beginLoadAdSeconds = MixedUtils.currentTimeSeconds()
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_LOAD, beginLoadAdSeconds)
        } else {
            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_RELOAD, beginLoadAdSeconds)
        }

        if (nativeTemplateView.get() != null) {
            if (mNativeAd == null) {
                if (CommFigs.WEB_CONNECTION_STATE != ConnectionState.DISCONNECTED) {
                    nativeTemplateView.get()?.findViewById<View>(R.id.nativeAdShimmerEffect)?.visibility = View.VISIBLE
                } else {
                    nativeTemplateView.get()?.visibility = View.GONE
                    templateContainerView.get()?.visibility = View.GONE
                    placeHolderContainerView.get()?.visibility = View.VISIBLE
                }
            } else {
                return
            }
        }

        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🟡: REQUEST BEGIN", CommFigs.LOG_TAG_NATIVE_AD)

        val builder = AdLoader.Builder(CommApplication.appContext, adUnitId!!)
        builder.forNativeAd { nativeAd ->
            admobErrorCode = null
            requestStatus = AdvertsRequestStatus.RESPONSE_OK

            requestSuccessMillis = System.currentTimeMillis()
            if (CommFigs.IS_SHOW_TEST_OPTION)
                DebugLogCustom.logd(getAdsName() + " 🟢: REQUEST SUCCESS", CommFigs.LOG_TAG_NATIVE_AD)

            FirebaseAssist.instance.logLoadAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logLoadSpecificAd(adTag, FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_LOADED, beginLoadAdSeconds)

            nativeAd.setOnPaidEventListener { adValue: AdValue ->
                val mediationAdapter = nativeAd.responseInfo?.mediationAdapterClassName
                sendLogAdverts(adValue, mediationAdapter)
            }
            mNativeAd = nativeAd
            onShowNativeAdCallback?.invoke(true)
            onShowNativeAdCallback = null

            nativeTemplateView.get()?.let {
                showNativeAdverts(it, templateContainerView.get())
            }
        }

        val adOptions =
            NativeAdOptions
                .Builder()
                .setAdChoicesPlacement(adChoicesPlacement)
                .setVideoOptions(VideoOptions.Builder().setStartMuted(muteMedia).build())
                .build()
        builder.withNativeAdOptions(adOptions)

        val adLoader =
            builder
                .withAdListener(
                    object : AdListener() {
                        override fun onAdClicked() {
                            super.onAdClicked()
                            DebugLogCustom.logd(getAdsName() + " 🟢: Native ad clicked", CommFigs.LOG_TAG_NATIVE_AD)
                            onClickNativeAdCallback?.invoke()

                            FirebaseAssist.instance.logClickAd(FirebaseAssist.AD_NATIVE, beginLoadAdSeconds)
                            if (adTag.isNotEmpty())
                                FirebaseAssist.instance.logClickSpecificAd(adTag, FirebaseAssist.AD_NATIVE, beginLoadAdSeconds)

                            LocalBroadcastManager
                                .getInstance(CommApplication.appContext)
                                .sendBroadcast(
                                    Intent().apply {
                                        action = AdvertsInstance.ACTION_AD_CLICKED
                                        putExtra(AdvertsInstance.ACTION_AD_CLICKED_AD_ID, adUnitId)
                                    },
                                )
                        }

                        override fun onAdImpression() {
                            super.onAdImpression()
                            DebugLogCustom.logd(getAdsName() + " 🟢: Native ad impression", CommFigs.LOG_TAG_NATIVE_AD)
                        }

                        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                            admobErrorCode = loadAdError.code
                            super.onAdFailedToLoad(loadAdError)

                            if (CommFigs.IS_SHOW_TEST_OPTION)
                                DebugLogCustom.logd(
                                    getAdsName() + " 🔴: REQUEST ERROR " + loadAdError.code + " -> " + loadAdError.message,
                                    CommFigs.LOG_TAG_NATIVE_AD,
                                )

                            val newAdId = findNextAdsId(adUnitId)
                            if (!newAdId.isNullOrEmpty()) {
                                adUnitId = newAdId
                                requestNativeAdverts(muteMedia = muteMedia)
                                return
                            }

                            if (mNativeAd != null) return

                            requestStatus = AdvertsRequestStatus.RESPONSE_ERROR

                            nativeTemplateView.get()?.visibility = View.GONE
                            templateContainerView.get()?.visibility = View.GONE
                            placeHolderContainerView.get()?.visibility = View.VISIBLE

                            onShowNativeAdCallback?.invoke(false)
                            onShowNativeAdCallback = null

                            if (nativeTemplateView.get() != null) {
                                FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
                                if (adTag.isNotEmpty())
                                    FirebaseAssist.instance.logShowSpecificAd(
                                        adTag,
                                        FirebaseAssist.AD_NATIVE,
                                        FirebaseAssist.AD_SHOW_FAILED,
                                        admobErrorCode,
                                    )
                            }

                            FirebaseAssist.instance.logLoadAd(
                                FirebaseAssist.AD_NATIVE,
                                FirebaseAssist.AD_LOAD_FAILED,
                                beginLoadAdSeconds,
                                loadAdError.code,
                            )
                            if (adTag.isNotEmpty())
                                FirebaseAssist.instance.logLoadSpecificAd(
                                    adTag,
                                    FirebaseAssist.AD_NATIVE,
                                    FirebaseAssist.AD_LOAD_FAILED,
                                    beginLoadAdSeconds,
                                    loadAdError.code,
                                )
                        }
                    },
                ).build()

        adLoader.loadAd(AdRequest.Builder().build())
    }

    fun showNativeAdverts(
        template: NativeTemplateView,
        templateContainer: View? = null,
        placeHolderContainer: View? = null,
        stopShimmering: Boolean = false,
        muteMedia: Boolean = true,
        onShowNativeAdCallback: ((Boolean) -> Unit)? = null,
    ) {
        val advertsConfig = AdvertsConfig.instance
        if (advertsConfig.isHideAd || !advertsConfig.isEnableNativeAds) {
            nativeTemplateView.get()?.visibility = View.GONE
            templateContainerView.get()?.visibility = View.GONE
            placeHolderContainerView.get()?.visibility = View.VISIBLE
            onShowNativeAdCallback?.invoke(true)
            return
        }

        nativeTemplateView = WeakReference(template)
        if (templateContainer != null)
            templateContainerView = WeakReference(templateContainer)
        if (placeHolderContainer != null)
            placeHolderContainerView = WeakReference(placeHolderContainer)

        if (mNativeAd == null &&
            requestStatus == AdvertsRequestStatus.RESPONSE_ERROR &&
            System.currentTimeMillis() - lastRequestLoadAdMillis < 2 * CommFigs.MILLIS_SECOND
        ) {
            nativeTemplateView.get()?.visibility = View.GONE
            templateContainerView.get()?.visibility = View.GONE
            placeHolderContainerView.get()?.visibility = View.VISIBLE
            FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            if (adTag.isNotEmpty())
                FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_SHOW_FAILED, admobErrorCode)
            onShowNativeAdCallback?.invoke(false)
            return
        }

        if (!isAdvertsAvailable) {
            nativeTemplateView.get()?.findViewById<ViewStub>(R.id.stubShimmerEffect)?.visibility = View.VISIBLE
            if (stopShimmering) {
                nativeTemplateView.get()?.findViewById<ShimmerFrameLayout>(R.id.nativeAdShimmerEffect)?.hideShimmer()
            }
            requestNativeAdverts(muteMedia = muteMedia)
            this.onShowNativeAdCallback = onShowNativeAdCallback
            return
        }
        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🔵: DISPLAY", CommFigs.LOG_TAG_NATIVE_AD)

        FirebaseAssist.instance.logShowAd(FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_SHOW_SUCCESS)
        if (adTag.isNotEmpty())
            FirebaseAssist.instance.logShowSpecificAd(adTag, FirebaseAssist.AD_NATIVE, FirebaseAssist.AD_SHOW_SUCCESS)
        nativeTemplateView.get()?.findViewById<View>(R.id.nativeAdShimmerEffect)?.let { shimmerEffect ->
            if (shimmerEffect.isVisible) {
                shimmerEffect.visibility = View.GONE
            }
        }
        nativeTemplateView.get()?.visibility = View.VISIBLE
        templateContainerView.get()?.visibility = View.VISIBLE
        placeHolderContainerView.get()?.visibility = View.GONE
        val styles = NativeTemplateStyle.Builder().withMainBackgroundColor(0xFF0000.toDrawable()).build()
        nativeTemplateView.get()?.setStyles(styles)
        mNativeAd?.let { nativeTemplateView.get()?.setNativeAd(it) }

        onShowNativeAdCallback?.invoke(true)
    }

    fun destroyNativeAd() {
        if (CommFigs.IS_SHOW_TEST_OPTION)
            DebugLogCustom.logd(getAdsName() + " 🔵: DESTROY", CommFigs.LOG_TAG_NATIVE_AD)
        mNativeAd?.destroy()
        nativeTemplateView.get()?.destroyNativeAd()
    }

    fun setOnClickNativeAdCallback(onClickNativeAdCallback: (() -> Unit)?) {
        this.onClickNativeAdCallback = onClickNativeAdCallback
    }
}

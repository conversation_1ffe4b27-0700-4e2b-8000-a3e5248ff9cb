package amobi.module.common.advertisements.native_ad

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.configs.CommFigs
import amobi.module.common.utils.DebugLogCustom
import android.view.View
import com.google.android.gms.ads.nativead.NativeAdOptions
import kotlin.math.max

object AdvertsManagerNative {
    const val ADCHOICES_BOTTOM_RIGHT = NativeAdOptions.ADCHOICES_BOTTOM_RIGHT
    const val ADCHOICES_TOP_RIGHT = NativeAdOptions.ADCHOICES_TOP_RIGHT
    const val ADCHOICES_BOTTOM_LEFT = NativeAdOptions.ADCHOICES_BOTTOM_LEFT
    const val ADCHOICES_TOP_LEFT = NativeAdOptions.ADCHOICES_TOP_LEFT

    fun requestNativeAdverts(
        nativeAdArray: ArrayList<AdvertsInstanceNative>,
        size: Int,
        listAdsID: Array<String>,
        adChoicesPlacement: Int = NativeAdOptions.ADCHOICES_BOTTOM_RIGHT,
        muteMedia: Boolean = true,
        adTag: String = "",
    ) {
        if (!AdvertsConfig.instance.isEnableNativeAds || AdvertsConfig.instance.isHideAd) return
        for (i in 0 until size) {
            val nativeAd = nativeAdArray.getOrNull(i)
            if (nativeAd?.isAdvertsAvailable == true || nativeAd?.isAdvertsLoading == true) continue
            val adInstance = AdvertsInstanceNative(listAdsID, adChoicesPlacement, adTag = adTag)
            if (nativeAd == null) {
                nativeAdArray.add(adInstance)
            } else {
                nativeAd.destroyNativeAd()
                nativeAdArray[i] = adInstance
            }
            adInstance.requestNativeAdverts(muteMedia = muteMedia)
        }
    }

    fun checkNativeAdvertsAvailable(nativeAdArray: ArrayList<AdvertsInstanceNative>): Boolean {
        if (!AdvertsConfig.instance.isEnableNativeAds || AdvertsConfig.instance.isHideAd) return true
        if (nativeAdArray.isEmpty()) return false
        val adNative = nativeAdArray.firstOrNull() ?: return false
        return adNative.isAdvertsAvailable
    }

    fun checkNativeAdvertsAvailableOrLoading(nativeAdArray: ArrayList<AdvertsInstanceNative>): Boolean {
        if (!AdvertsConfig.instance.isEnableNativeAds || AdvertsConfig.instance.isHideAd) return true
        if (nativeAdArray.isEmpty()) return false
        val adNative = nativeAdArray.firstOrNull() ?: return false
        return adNative.isAdvertsAvailable || adNative.isAdvertsLoading
    }

    fun popShowNativeAdverts(
        nativeAdNativeTemplateView: NativeTemplateView,
        templateContainer: View? = null,
        placeHolderContainer: View? = null,
        nativeAdArray: ArrayList<AdvertsInstanceNative>,
        listAdsID: Array<String>,
        adChoicesPlacement: Int? = null,
        stopShimmering: Boolean = false,
        fillCache: Boolean = true,
        muteMedia: Boolean = true,
        adTag: String = "",
    ): AdvertsInstanceNative? {
        if (AdvertsConfig.instance.isHideAd || !AdvertsConfig.instance.isEnableNativeAds) {
            nativeAdNativeTemplateView.visibility = View.GONE
            templateContainer?.visibility = View.GONE
            return null
        }

        val adChoicesOptions = adChoicesPlacement ?: if (nativeAdNativeTemplateView.isAdChoiceTop) NativeAdOptions.ADCHOICES_TOP_RIGHT else NativeAdOptions.ADCHOICES_BOTTOM_RIGHT

        val nativeAdFromArray = nativeAdArray.firstOrNull()
        if (nativeAdFromArray == null || !nativeAdFromArray.isAdvertsAvailable) {
            DebugLogCustom.logd("Need to REQUEST new native adverts " + nativeAdArray.size, CommFigs.LOG_TAG_NATIVE_AD)
            val advertsInstanceNative = AdvertsInstanceNative(listAdsID, adChoicesOptions, adTag = adTag)
            advertsInstanceNative.requestNativeAdverts(muteMedia = muteMedia)
            advertsInstanceNative.showNativeAdverts(nativeAdNativeTemplateView, templateContainer, placeHolderContainer) {
                if (fillCache)
                    replaceExpiredNativeAdverts(nativeAdArray, listAdsID, adChoicesOptions, adTag = adTag)
            }
            return advertsInstanceNative
        }

        val advertsNativeManager = nativeAdArray.removeAt(0)
        advertsNativeManager.showNativeAdverts(
            nativeAdNativeTemplateView,
            templateContainer,
            placeHolderContainer,
            stopShimmering = stopShimmering,
        ) {
            if (fillCache) {
                val newManager = AdvertsInstanceNative(listAdsID, adChoicesOptions, adTag = adTag)
                newManager.requestNativeAdverts(muteMedia = muteMedia)
                nativeAdArray.add(newManager)
            }
        }

        return advertsNativeManager
    }

    private var lastReplaceExpiredNativeAdverts = 0L

    private fun replaceExpiredNativeAdverts(
        nativeAdArray: ArrayList<AdvertsInstanceNative>,
        listAdsID: Array<String>,
        adChoicesPlacement: Int = NativeAdOptions.ADCHOICES_BOTTOM_RIGHT,
        muteMedia: Boolean = true,
        adTag: String = "",
    ) {
        if (System.currentTimeMillis() - lastReplaceExpiredNativeAdverts < CommFigs.MILLIS_SECOND) return
        lastReplaceExpiredNativeAdverts = System.currentTimeMillis()

        if (nativeAdArray.firstOrNull()?.isAdmobNoFill == true) return

        for (i in 0 until max(nativeAdArray.size, 1)) {
            val nativeAd = nativeAdArray.getOrNull(i)
            if (nativeAd?.isAdvertsAvailable == true || nativeAd?.isAdvertsLoading == true) continue
            nativeAd?.destroyNativeAd()
            val adInstance = AdvertsInstanceNative(listAdsID, adChoicesPlacement, adTag = adTag)
            if (i >= nativeAdArray.size)
                nativeAdArray.add(adInstance)
            else
                nativeAdArray[i] = adInstance
            adInstance.requestNativeAdverts(muteMedia = muteMedia)
        }
    }
}

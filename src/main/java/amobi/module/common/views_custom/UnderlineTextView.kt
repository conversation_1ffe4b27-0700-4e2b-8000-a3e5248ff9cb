package amobi.module.common.views_custom

import android.content.Context
import android.graphics.Paint.UNDERLINE_TEXT_FLAG
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView

class UnderlineTextView : AppCompatTextView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        paintFlags = paintFlags or UNDERLINE_TEXT_FLAG
    }
}

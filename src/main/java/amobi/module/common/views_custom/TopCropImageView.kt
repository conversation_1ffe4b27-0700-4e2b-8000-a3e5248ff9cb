package amobi.module.common.views_custom

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

class TopCropImageView : AppCompatImageView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle)

    init {
        scaleType = ScaleType.MATRIX
    }

    override fun onLayout(
        changed: Boolean,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
    ) {
        super.onLayout(changed, left, top, right, bottom)
        recomputeImgMatrix()
    }

    override fun setFrame(
        l: Int,
        t: Int,
        r: Int,
        b: Int,
    ): Boolean {
        recomputeImgMatrix()
        return super.setFrame(l, t, r, b)
    }

    private fun recomputeImgMatrix() {
        if (drawable == null) return
        val matrix = imageMatrix

        val viewWidth = width - paddingLeft - paddingRight
        val viewHeight = height - paddingTop - paddingBottom
        val drawableWidth = drawable.intrinsicWidth
        val drawableHeight = drawable.intrinsicHeight

        val scale =
            if (drawableWidth * viewHeight > drawableHeight * viewWidth) {
                viewHeight.toFloat() / drawableHeight.toFloat()
            } else {
                viewWidth.toFloat() / drawableWidth.toFloat()
            }

        matrix.setScale(scale, scale)
        imageMatrix = matrix
    }
}

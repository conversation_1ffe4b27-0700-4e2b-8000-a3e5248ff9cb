package amobi.module.compose.extentions

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import android.content.Context
import android.content.res.Configuration
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import kotlin.random.Random

@Preview(
    locale = PreviewAssist.PREVIEW_LOCALE,
    showBackground = true,
    showSystemUi = false,
    uiMode = PreviewAssist.PREVIEW_UI_MODE,
    backgroundColor = PreviewAssist.PREVIEW_BG_COLOR,
    device = Devices.PIXEL_3A_XL,
)
annotation class AppPreview

@Preview(
    locale = PreviewAssist.PREVIEW_LOCALE,
    showBackground = true,
    showSystemUi = false,
    uiMode = PreviewAssist.PREVIEW_UI_MODE,
    backgroundColor = PreviewAssist.PREVIEW_BG_COLOR,
    widthDp = PreviewAssist.PREVIEW_WIDTH_EXTRA,
    heightDp = PreviewAssist.PREVIEW_HEIGHT,
)
annotation class AppPreviewWidthExtra

@Preview(
    locale = PreviewAssist.PREVIEW_LOCALE,
    showBackground = true,
    showSystemUi = false,
    uiMode = PreviewAssist.PREVIEW_UI_MODE,
    backgroundColor = PreviewAssist.PREVIEW_BG_COLOR,
    widthDp = PreviewAssist.PREVIEW_WIDTH,
    heightDp = PreviewAssist.PREVIEW_HEIGHT_EXTRA,
)
annotation class AppPreviewHeightExtra

object PreviewAssist {
    const val PREVIEW_LOCALE = "en"

    //    const val PREVIEW_UI_MODE = Configuration.UI_MODE_NIGHT_NO
    const val PREVIEW_UI_MODE = Configuration.UI_MODE_NIGHT_YES
    const val PREVIEW_BG_COLOR = 0x30026290.toLong()
    const val PREVIEW_WIDTH = 360
    const val PREVIEW_WIDTH_EXTRA = 1200
    const val PREVIEW_HEIGHT = 800
    const val PREVIEW_HEIGHT_EXTRA = 2000

    // =============== SIZE DEBUG ==================
    val SIZE_DEBUG: Boolean
        get() {
            if (!CommFigs.IS_SHOW_TEST_OPTION) return false
            return false // tssst
        }
    val randomColorDebug: Color
        get() {
            return Color(Random.nextFloat(), Random.nextFloat(), Random.nextFloat(), 0.3f)
        }

    var IS_PREVIEW = false

    fun initVariables(context: Context) {
        if (CommApplication.nullableInstance != null) {
            throw IllegalStateException("CommApplication is already initialized")
        }
        PrefAssist.initPreviewPreferences(context)
        PrefAssist.defaultValueString = PreviewAssist::previewDefString
        PrefAssist.defaultValueBoolean = PreviewAssist::previewDefBoolean
        PrefAssist.defaultValueInt = PreviewAssist::previewDefInt
        PrefAssist.defaultValueLong = PreviewAssist::previewDefLong
        CommFigs.setupBuildConfigs(true, "Alpha")
        CommApplication.previewContext = context
        IS_PREVIEW = true
    }

    private fun previewDefString(key: String): String = ""

    private fun previewDefLong(key: String): Long = 0L

    private fun previewDefBoolean(key: String): Boolean = false

    private fun previewDefInt(key: String): Int = 0
}

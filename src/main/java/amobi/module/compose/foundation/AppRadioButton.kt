package amobi.module.compose.foundation

import amobi.module.compose.theme.AppColors
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonColors
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.runtime.Composable

@Composable
fun AppRadioButton(
    selected: Boolean,
    colors: RadioButtonColors =
        RadioButtonDefaults.colors(
            selectedColor = AppColors.current.primary,
            unselectedColor = AppColors.current.text,
        ),
    onClick: (() -> Unit)? = null,
) {
    RadioButton(
        selected = selected,
        colors = colors,
        onClick = { onClick?.invoke() },
    )
}

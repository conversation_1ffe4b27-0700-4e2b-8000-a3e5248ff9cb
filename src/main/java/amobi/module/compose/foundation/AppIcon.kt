package amobi.module.compose.foundation

import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.appClickable
import amobi.module.compose.extentions.conditional
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppSize
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp

@Composable
fun AppIcon(
    @DrawableRes icon: Int,
    modifier: Modifier = Modifier,
    size: Dp = AppSize.ICON_SIZE, // former iconSize
    clickZone: Dp = AppSize.MIN_TOUCH_SIZE, // former iconClickZone
    contentDescription: String? = null,
    tint: Color? = AppColors.current.iconColor, // former color
    onClick: (() -> Unit)? = null,
) {
    AppIcon(
        icon = painterResource(id = icon),
        modifier = modifier,
        size = size,
        clickZone = clickZone,
        contentDescription = contentDescription,
        tint = tint,
        onClick = onClick,
    )
}

@Composable
fun AppIcon(
    icon: Painter,
    modifier: Modifier = Modifier,
    size: Dp = AppSize.ICON_SIZE,
    clickZone: Dp = AppSize.MIN_TOUCH_SIZE,
    contentDescription: String? = null,
    tint: Color? = AppColors.current.iconColor,
    onClick: (() -> Unit)? = null,
) {
    if (onClick == null) {
        return Image(
            painter = icon,
            contentDescription = contentDescription,
            contentScale = ContentScale.Fit,
            colorFilter = if (tint == null) null else ColorFilter.tint(color = tint),
            modifier =
                modifier
                    .conditional(PreviewAssist.SIZE_DEBUG) { background(PreviewAssist.randomColorDebug) }
                    .size(size),
        )
    }

    Image(
        painter = icon,
        modifier =
            modifier
                .conditional(PreviewAssist.SIZE_DEBUG) { background(PreviewAssist.randomColorDebug) }
                .size(clickZone)
                .clip(shape = CircleShape)
                .appClickable(onClick = onClick)
                .padding(
                    clickZone
                        .minus(size)
                        .div(2),
                ),
        contentDescription = contentDescription,
        contentScale = ContentScale.Fit,
        colorFilter = if (tint == null) null else ColorFilter.tint(color = tint),
    )
}

@Composable
fun AppIcon(
    imageVector: ImageVector,
    modifier: Modifier = Modifier,
    size: Dp = AppSize.ICON_SIZE,
    clickZone: Dp = AppSize.MIN_TOUCH_SIZE,
    contentDescription: String? = null,
    tint: Color = AppColors.current.iconColor,
    onClick: (() -> Unit)? = null,
) {
    if (onClick == null) {
        return Icon(
            imageVector = imageVector,
            contentDescription = contentDescription,
            modifier =
                modifier
                    .conditional(PreviewAssist.SIZE_DEBUG) { background(PreviewAssist.randomColorDebug) }
                    .size(size),
            tint = tint,
        )
    }

    Icon(
        imageVector = imageVector,
        contentDescription = contentDescription,
        modifier =
            modifier
                .conditional(PreviewAssist.SIZE_DEBUG) { background(PreviewAssist.randomColorDebug) }
                .size(clickZone)
                .clip(shape = CircleShape)
                .appClickable(onClick = onClick)
                .padding(
                    clickZone
                        .minus(size)
                        .div(2),
                ),
        tint = tint,
    )
}

package amobi.module.compose.complex_views

import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.clipCorner
import amobi.module.compose.extentions.minHeight
import amobi.module.compose.extentions.minWidth
import amobi.module.compose.foundation.AppButtonText
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog

@AppPreview
@Composable
fun AppConfirmDialogPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        AppConfirmDialog(
            title = "Title",
            message = "Message",
            positiveButtonText = "Ok",
            negativeButtonText = "Cancel",
            onPositiveClick = {},
            onNegativeClick = {},
            onDismissRequest = {},
        )
    }
}

@Composable
fun AppConfirmDialogCore(
    title: String,
    message: String? = null,
    positiveButtonText: String? = null,
    negativeButtonText: String? = null,
    positiveButtonColor: Color = AppColors.current.buttonActive,
    customContent: @Composable () -> Unit = {},
    onPositiveClick: (() -> Unit)? = null,
    onNegativeClick: (() -> Unit)? = null,
) {
    AppColumn(
        modifier =
            Modifier
                .wrapContentSize()
                .clipCorner(10.dp)
                .background(MaterialTheme.colorScheme.background)
                .padding(18.dp),
    ) {
        AppText(
            title,
            modifier =
                Modifier
                    .fillMaxWidth(),
            fontSize = 18.sp,
            lineHeight = 19.sp,
            textAlign = TextAlign.Start,
            fontWeight = FontWeight.W700,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            color = AppColors.current.titleText,
        )
        AppSpacer(16.dp)
        if (message != null)
            AppText(
                message,
                modifier = Modifier.padding(bottom = 10.dp),
                fontSize = 14.sp,
                textAlign = TextAlign.Start,
                overflow = TextOverflow.Ellipsis,
                color = AppColors.current.text,
                lineHeight = 18.sp,
            )

        customContent()

        AppRow(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(top = 25.dp),
            horizontalArrangement = Arrangement.End,
        ) {
            if (negativeButtonText != null && onNegativeClick != null) {
                AppButtonText(
                    modifier =
                        Modifier
                            .minWidth(80.dp)
                            .minHeight(AppSize.MIN_TOUCH_SIZE),
                    text = negativeButtonText,
                    contentPadding =
                        PaddingValues(
                            horizontal = 16.dp,
                            vertical = 8.dp,
                        ),
                    textColor = AppColors.current.text,
                    colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent),
                    colorShadow = null,
                    onClick = { onNegativeClick() },
                )
                AppSpacer(8.dp)
            }

            if (positiveButtonText != null && onPositiveClick != null)
                AppButtonText(
                    modifier =
                        Modifier
                            .minWidth(80.dp)
                            .minHeight(AppSize.MIN_TOUCH_SIZE),
                    text = positiveButtonText,
                    colors =
                        ButtonDefaults.buttonColors(
                            containerColor = positiveButtonColor,
                            disabledContainerColor = AppColors.current.buttonInactive,
                            contentColor = AppColors.current.buttonText,
                            disabledContentColor = AppColors.current.buttonInactiveText,
                        ),
                    fontSize = AppFontSize.BODY2,
                    contentPadding =
                        PaddingValues(
                            horizontal = 16.dp,
                            vertical = 8.dp,
                        ),
                    onClick = { onPositiveClick() },
                )
        }
    }
}

@Composable
fun AppConfirmDialog(
    title: String,
    message: String? = null,
    positiveButtonText: String? = null,
    positiveButtonColor: Color = AppColors.current.buttonActive,
    negativeButtonText: String? = null,
    customContent: @Composable () -> Unit = {},
    onPositiveClick: (() -> Unit)? = null,
    onNegativeClick: (() -> Unit)? = null,
    onDismissRequest: () -> Unit = { },
) {
    Dialog(onDismissRequest = { onDismissRequest() }) {
        AppConfirmDialogCore(
            title = title,
            message = message,
            positiveButtonText = positiveButtonText,
            positiveButtonColor = positiveButtonColor,
            negativeButtonText = negativeButtonText,
            customContent = customContent,
            onPositiveClick = onPositiveClick,
            onNegativeClick = onNegativeClick,
        )
    }
}

package amobi.module.compose.complex_views

import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppLoadingAnim
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppThemeWrapper
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog

@AppPreview
@Composable
fun ProgressLoadingDialogPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        AppLoadingDialog("Loading, Please wait for very long time")
    }
}

@Composable
fun AppLoadingDialogCore(
    message: String,
    colorLoading: Color = AppColors.current.primary,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(10.dp),
        colors =
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.background,
            ),
    ) {
        AppRow(
            modifier =
                Modifier
                    .padding(horizontal = 16.dp, vertical = 20.dp)
                    .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start,
        ) {
            AppLoadingAnim(
                modifier = Modifier.size(50.dp),
                color = colorLoading,
            )
            AppText(
                message,
                modifier =
                    Modifier
                        .padding(start = 16.dp)
                        .weight(1f),
                fontSize = 16.sp,
                textAlign = TextAlign.Start,
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.W600,
                color = AppColors.current.text,
                lineHeight = 16.sp,
            )
        }
    }
}

@Composable
fun AppLoadingDialog(
    message: String,
    colorLoading: Color = AppColors.current.primary,
    onDismissRequest: () -> Unit = {},
) {
    Dialog(onDismissRequest = { onDismissRequest() }) {
        AppLoadingDialogCore(message, colorLoading)
    }
}

package amobi.module.otp

import android.content.Context
import android.opengl.ETC1.isValid
import android.util.Log

object OtpHelper {

    fun setSecret(context: Context, secret: String) {
        // Initialize secure storage
        val secureStorage = SecureStorage(context)

//        Log.d("Otp<PERSON>el<PERSON>", "setSecret: $secret")

        // Save the secret
        secureStorage.saveSecret("otp_secret", secret)
    }

    fun getOTP(context: Context): String {
        // Initialize secure storage
        val secureStorage = SecureStorage(context)

        // Get existing secret or generate new one
        var secret = secureStorage.getSecret("otp_secret")
        if (secret == null) {
            // Generate new secret if none exists
            secret = TOTPGenerator.generateSecret()
            secureStorage.saveSecret("otp_secret", secret)
        }

        // Initialize TOTP generator with the secret
        val totpGenerator = TOTPGenerator(secret)

        // Generate current OTP code
        val currentCode = totpGenerator.generate()

        // Example of verifying a code

//        Log.d("<PERSON>tp<PERSON><PERSON><PERSON>", "Secret: $secret")
//        Log.d("Otp<PERSON>el<PERSON>", "Current Code: $currentCode")
//        Log.d("OtpHelper", "Is Valid: ${totpGenerator.verify(currentCode)}")

        // The OTP code will change every 30 seconds
        // You can use this in your UI or wherever needed
        return currentCode
    }
}
plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace 'amobi.module.otp'

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }
    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.security.crypto

    implementation libs.androidx.constraintlayout
    implementation libs.androidx.appcompat
    implementation libs.androidx.material

    implementation libs.lifecycle.extensions
    implementation libs.lifecycle.common.java8
}
package amobi.module.openai.client

import amobi.module.openai.api.edits.Edit
import amobi.module.openai.api.edits.EditsRequest

/**
 * Given a prompt and an instruction, the model will return an edited version of the prompt.
 */
public interface Edits {

    /**
     * Creates a new edit for the provided input, instruction, and parameters.
     */
    @Deprecated("Edits is deprecated. Chat completions is the recommend replacement.")
    public suspend fun edit(request: EditsRequest): Edit
}

package amobi.module.openai.client.extension

import amobi.module.openai.api.ExperimentalOpenAI
import amobi.module.openai.api.chat.ChatChunk
import amobi.module.openai.api.chat.ChatMessage
import amobi.module.openai.client.extension.internal.ChatMessageAssembler

/**
 * Merges a list of [ChatChunk]s into a single consolidated [ChatMessage].
 */
@ExperimentalOpenAI
public fun List<ChatChunk>.mergeToChatMessage(): ChatMessage {
    require(isNotEmpty()) { "ChatChunks List must not be empty" }
    return fold(ChatMessageAssembler()) { assembler, chatChunk -> assembler.merge(chatChunk) }.build()
}

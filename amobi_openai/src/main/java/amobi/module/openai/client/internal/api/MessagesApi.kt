package amobi.module.openai.client.internal.api

import amobi.module.openai.api.core.PaginatedList
import amobi.module.openai.api.core.RequestOptions
import amobi.module.openai.api.core.SortOrder
import amobi.module.openai.api.message.Message
import amobi.module.openai.api.message.MessageId
import amobi.module.openai.api.message.MessageRequest
import amobi.module.openai.api.thread.ThreadId
import amobi.module.openai.client.Messages
import amobi.module.openai.client.internal.extension.beta
import amobi.module.openai.client.internal.extension.requestOptions
import amobi.module.openai.client.internal.http.HttpRequester
import amobi.module.openai.client.internal.http.perform
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*

internal class MessagesApi(val requester: HttpRequester) : Messages {
    override suspend fun message(
        threadId: ThreadId,
        request: MessageRequest,
        requestOptions: RequestOptions?
    ): Message {
        return requester.perform {
            it.post {
                url(path = "${ApiPath.Threads}/${threadId.id}/messages")
                setBody(request)
                contentType(ContentType.Application.Json)
                beta("assistants", 2)
                requestOptions(
                    requestOptions
                )
            }.body()
        }
    }

    override suspend fun message(threadId: ThreadId, messageId: MessageId, requestOptions: RequestOptions?): Message {
        return requester.perform {
            it.get {
                url(path = "${ApiPath.Threads}/${threadId.id}/messages/${messageId.id}")
                beta("assistants", 2)
                requestOptions(
                    requestOptions
                )
            }.body()
        }
    }

    override suspend fun message(
        threadId: ThreadId,
        messageId: MessageId,
        metadata: Map<String, String>?,
        requestOptions: RequestOptions?
    ): Message {
        return requester.perform {
            it.post {
                url(path = "${ApiPath.Threads}/${threadId.id}/messages/${messageId.id}")
                metadata?.let { meta ->
                    setBody(mapOf("metadata" to meta))
                    contentType(ContentType.Application.Json)
                }
                beta("assistants", 2)
                requestOptions(requestOptions)
            }.body()
        }
    }

    override suspend fun messages(
        threadId: ThreadId,
        limit: Int?,
        order: SortOrder?,
        after: MessageId?,
        before: MessageId?,
        requestOptions: RequestOptions?
    ): PaginatedList<Message> {
        return requester.perform {
            it.get {
                url(path = "${ApiPath.Threads}/${threadId.id}/messages") {
                    limit?.let { value -> parameter("limit", value) }
                    order?.let { value -> parameter("order", value.order) }
                    before?.let { value -> parameter("before", value.id) }
                    after?.let { value -> parameter("after", value.id) }
                }
                beta("assistants", 2)
                requestOptions(requestOptions)
            }.body()
        }
    }
}

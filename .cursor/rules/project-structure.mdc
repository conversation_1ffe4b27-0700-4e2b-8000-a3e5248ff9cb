---
description: 
globs: 
alwaysApply: true
---
[*.kt]
# Prefer App-prefixed foundation components over basic Compose components
prefer_components = {
    "Column": "AppColumn",
    "Box": "AppBox", 
    "Row": "AppRow",
    "Text": "AppText",
    "Button": "AppButton",
    "Icon": "AppIcon",
    "Divider": "AppDivider",
    "Switch": "AppSwitch",
    "RadioButton": "AppRadioButton"
}

# Enforce using App foundation components
deny = [
    "androidx.compose.foundation.layout.Column",
    "androidx.compose.foundation.layout.Box",
    "androidx.compose.foundation.layout.Row",
    "androidx.compose.material3.Text",
    "androidx.compose.material3.Button",
    "androidx.compose.material3.Icon",
    "androidx.compose.material3.Divider",
    "androidx.compose.material3.Switch",
    "androidx.compose.material3.RadioButton"
]

# Allow exceptions in foundation components themselves
[**/foundation/*.kt]
allow = [
    "androidx.compose.foundation.layout.*",
    "androidx.compose.material3.*"
]
